# 网格参数评估指南

## 为什么需要评估网格参数？

网格参数直接影响：
- **API调用次数** = 直接影响成本
- **数据覆盖率** = 影响数据完整性  
- **重复数据量** = 影响数据质量
- **扫描效率** = 影响运行时间

## 快速评估方法

### 1. 使用评估工具

```bash
# 快速评估
python quick_grid_evaluator.py

# 选择模式:
# 1. 洛杉矶大区域评估 (29,315 km²) - 用于生产环境评估
# 2. 小范围测试评估 (100 km²) - 用于参数测试
# 3. 自定义区域评估 - 用于特定区域
# 4. 参数敏感性测试 - 用于优化现有参数
```

### 2. 关键评估指标

| 指标 | 理想范围 | 说明 |
|------|----------|------|
| **覆盖率** | 1.0-1.5倍 | 1.0倍=刚好覆盖，1.5倍=适度重叠 |
| **重叠率** | 10-30% | 保证覆盖完整性，避免过度重叠 |
| **成本/km²** | <$0.1 | 每平方公里的API调用成本 |
| **效率评分** | >80分 | 综合评分，越高越好 |

## 参数优化策略

### 1. 网格间距优化

**经验法则**：
- **无重叠**: 间距 = 2 × 半径 (重叠率0%)
- **轻度重叠**: 间距 = 1.6 × 半径 (重叠率20%)  
- **适度重叠**: 间距 = 1.4 × 半径 (重叠率30%)

**推荐配置**：
```python
# 宏观扫描 - 优先成本效率
MACRO_SPACING = 1.6 * (MACRO_RADIUS / 1000)  # 轻度重叠

# 精细扫描 - 平衡覆盖和成本  
FINE_SPACING = 1.4 * (FINE_RADIUS / 1000)   # 适度重叠

# 增强扫描 - 优先覆盖完整性
ENHANCED_SPACING = 1.2 * (ENHANCED_RADIUS / 1000)  # 较多重叠
```

### 2. 搜索半径优化

**考虑因素**：
- **API限制**: 半径越大，单次返回结果越多
- **地理特征**: 城市密度、道路分布
- **业务需求**: 数据精度要求

**推荐配置**：
```python
# 基于区域密度调整
if area_density == "high":      # 高密度城市区域
    MACRO_RADIUS = 3000
    FINE_RADIUS = 800  
    ENHANCED_RADIUS = 400
elif area_density == "medium":  # 中密度郊区
    MACRO_RADIUS = 5000
    FINE_RADIUS = 1000
    ENHANCED_RADIUS = 500
else:                          # 低密度乡村
    MACRO_RADIUS = 8000
    FINE_RADIUS = 1500
    ENHANCED_RADIUS = 800
```

## 实际评估案例

### 案例1: 小范围测试区域 (100km²)

```
=== 评估结果 ===
宏观扫描: 间距10.0km, 1个网格, $0.03
精细扫描: 间距1.6km, 16个网格, $0.51  
增强扫描: 间距0.9km, 4个网格, $0.13
总成本: $0.67 (每km² $0.007)
```

**分析**：
- ✅ 成本合理，适合测试
- ✅ 覆盖率良好
- ⚠️ 宏观扫描网格太少，可能遗漏热点

**优化建议**：
- 减小宏观扫描间距到7km
- 增加网格数量以提高热点识别准确性

### 案例2: 参数敏感性分析

```
基准: 间距7.0km, 半径5000m, 25网格, $0.80

间距变化:
- 间距5.6km: +44%成本 (过密)
- 间距8.4km: -36%成本 (可能遗漏)

半径变化:  
- 半径4000m: 覆盖不足
- 半径6000m: 过度重叠41.7%
```

**分析**：
- 间距对成本影响很大 (±40%)
- 半径主要影响覆盖质量
- 当前配置相对平衡

## 参数调优流程

### 第一步：确定目标区域特征
```python
# 评估区域基本信息
area_km2 = 1000          # 区域面积
expected_density = "medium"  # 预期商店密度
budget_limit = 50.0      # 预算限制
```

### 第二步：运行快速评估
```bash
python quick_grid_evaluator.py
# 选择自定义区域评估
# 输入区域面积和热点比例
```

### 第三步：分析评估结果
- 检查效率评分 (目标>80分)
- 检查成本是否在预算内
- 检查覆盖率是否合理 (1.0-1.5倍)

### 第四步：参数微调
```python
# 如果成本过高
if cost > budget:
    increase_spacing()  # 增加间距
    
# 如果覆盖不足  
if coverage < 1.0:
    decrease_spacing()  # 减少间距
    # 或 increase_radius()  # 增加半径
    
# 如果重叠过多
if overlap > 0.4:
    increase_spacing()  # 增加间距
```

### 第五步：小范围验证
- 选择1-2平方公里区域进行实际API测试
- 验证参数设置的实际效果
- 根据结果进行最终调整

## 常见问题和解决方案

### Q1: 如何平衡成本和覆盖率？
**A**: 使用分阶段策略
- 宏观扫描：优先成本效率，允许部分遗漏
- 精细扫描：平衡成本和覆盖
- 增强扫描：优先覆盖完整性

### Q2: 网格重叠是否浪费？
**A**: 适度重叠是必要的
- 10-30%重叠可以避免边界遗漏
- 过度重叠(>40%)才是浪费
- 可以通过后期去重处理

### Q3: 如何处理不规则区域？
**A**: 
- 评估工具假设正方形区域，实际可能有差异
- 对于复杂形状，建议增加10-20%的安全边际
- 可以分割为多个规则子区域分别评估

### Q4: 参数需要多久调整一次？
**A**:
- 初次使用：需要仔细评估和测试
- 稳定运行后：每6个月检查一次
- 扩展到新区域：重新评估参数

## 最佳实践总结

1. **先评估，后执行** - 避免盲目设置参数
2. **小范围测试** - 用少量成本验证参数合理性  
3. **监控实际效果** - 记录实际成本和数据质量
4. **渐进式优化** - 基于实际结果逐步调整
5. **文档化配置** - 记录参数选择的原因和效果

通过系统性的参数评估，可以在保证数据质量的前提下，显著降低API调用成本。