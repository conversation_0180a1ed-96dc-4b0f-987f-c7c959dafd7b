#!/usr/bin/env python3
"""
测试H3子网格生成逻辑
验证h3.cell_to_children函数是否正确生成子网格
"""

import h3
import json
from pathlib import Path

def test_h3_child_generation():
    """测试H3子网格生成"""
    
    print("🔍 测试H3子网格生成逻辑")
    print("=" * 60)
    
    # 测试基本的H3子网格生成
    print("\n1. 基本H3子网格生成测试:")
    print("-" * 40)
    
    # 创建一个测试的H3索引 (res 5)
    test_lat, test_lng = 34.0522, -118.2437
    parent_h3 = h3.latlng_to_cell(test_lat, test_lng, 5)
    
    print(f"父网格 (res 5): {parent_h3}")
    print(f"父网格中心: {h3.cell_to_latlng(parent_h3)}")
    
    # 生成子网格 (res 7)
    child_h3_indexes = h3.cell_to_children(parent_h3, 7)
    print(f"子网格数量 (res 5 → res 7): {len(child_h3_indexes)}")
    
    # 验证子网格
    print("\n子网格详情:")
    for i, child_h3 in enumerate(list(child_h3_indexes)[:5]):  # 只显示前5个
        child_lat, child_lng = h3.cell_to_latlng(child_h3)
        child_boundary = h3.cell_to_boundary(child_h3)
        print(f"  {i+1}. {child_h3}")
        print(f"     中心: ({child_lat:.6f}, {child_lng:.6f})")
        print(f"     边界点数: {len(child_boundary)}")
        
        # 验证边界是否为六边形
        if len(child_boundary) != 6:
            print(f"     ❌ 警告: 边界不是六边形! 点数: {len(child_boundary)}")
        else:
            print(f"     ✅ 正确的六边形")
    
    if len(child_h3_indexes) > 5:
        print(f"  ... 还有 {len(child_h3_indexes) - 5} 个子网格")
    
    # 测试实际项目中的网格
    print(f"\n2. 实际项目网格测试:")
    print("-" * 40)
    
    test_projects = ["test-strong-overlap", "test-correct-radius"]
    
    for project_name in test_projects:
        config_file = Path("projects") / project_name / "project_config.json"
        grids_file = Path("projects") / project_name / "grids" / "level5_grids.json"
        
        if not config_file.exists() or not grids_file.exists():
            print(f"❌ 项目 {project_name} 不存在或缺少文件")
            continue
            
        print(f"\n项目: {project_name}")
        
        # 加载配置
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 加载Level 5网格
        with open(grids_file, 'r', encoding='utf-8') as f:
            level5_grids = json.load(f)
        
        print(f"  Level 5网格数量: {len(level5_grids)}")
        
        # 测试第一个网格的子网格生成
        if level5_grids:
            test_grid = level5_grids[0]
            parent_h3 = test_grid['h3_index']
            
            print(f"  测试网格: {parent_h3}")
            print(f"  网格中心: ({test_grid['latitude']:.6f}, {test_grid['longitude']:.6f})")
            
            # 生成Level 7子网格
            try:
                child_h3_indexes = h3.cell_to_children(parent_h3, config['h3_res_level2'])
                print(f"  ✅ 成功生成 {len(child_h3_indexes)} 个Level 7子网格")
                
                # 验证前几个子网格
                valid_count = 0
                invalid_count = 0
                
                for child_h3 in list(child_h3_indexes)[:10]:  # 检查前10个
                    try:
                        child_lat, child_lng = h3.cell_to_latlng(child_h3)
                        child_boundary = h3.cell_to_boundary(child_h3)
                        
                        if len(child_boundary) == 6:
                            valid_count += 1
                        else:
                            invalid_count += 1
                            print(f"    ❌ 异常子网格: {child_h3}, 边界点数: {len(child_boundary)}")
                            
                    except Exception as e:
                        invalid_count += 1
                        print(f"    ❌ 子网格处理失败: {child_h3}, 错误: {e}")
                
                print(f"  验证结果: {valid_count} 个正常, {invalid_count} 个异常")
                
            except Exception as e:
                print(f"  ❌ 子网格生成失败: {e}")
    
    # 测试分辨率转换
    print(f"\n3. 分辨率转换测试:")
    print("-" * 40)
    
    test_conversions = [
        (5, 6), (5, 7), (5, 8),
        (6, 7), (6, 8),
        (7, 8)
    ]
    
    for parent_res, child_res in test_conversions:
        try:
            test_h3 = h3.latlng_to_cell(34.0522, -118.2437, parent_res)
            children = h3.cell_to_children(test_h3, child_res)
            print(f"  res {parent_res} → res {child_res}: {len(children)} 个子网格")
            
            # 验证第一个子网格
            if children:
                first_child = list(children)[0]
                boundary = h3.cell_to_boundary(first_child)
                if len(boundary) == 6:
                    print(f"    ✅ 子网格形状正常")
                else:
                    print(f"    ❌ 子网格形状异常: {len(boundary)} 个边界点")
                    
        except Exception as e:
            print(f"  ❌ res {parent_res} → res {child_res} 转换失败: {e}")
    
    print(f"\n4. H3库版本信息:")
    print("-" * 40)
    print(f"H3库版本: {h3.__version__}")
    
    # 测试H3库的基本功能
    try:
        test_h3 = h3.latlng_to_cell(34.0522, -118.2437, 5)
        test_boundary = h3.cell_to_boundary(test_h3)
        print(f"基本功能测试: ✅ 正常 (边界点数: {len(test_boundary)})")
    except Exception as e:
        print(f"基本功能测试: ❌ 失败 - {e}")

if __name__ == "__main__":
    test_h3_child_generation()
