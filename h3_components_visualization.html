
<!DOCTYPE html>
<html>
<head>
    <title>H3 组件可视化测试</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        #map { height: 600px; width: 100%; margin-bottom: 20px; }
        .info-panel { background: #f0f0f0; padding: 15px; border-radius: 5px; margin-bottom: 10px; }
        .controls { margin-bottom: 20px; }
        .controls button { margin: 5px; padding: 8px 15px; cursor: pointer; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; }
        .stat-card { background: white; padding: 15px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .resolution-5 { color: #ff0000; }
        .resolution-6 { color: #00ff00; }
        .resolution-7 { color: #0000ff; }
        .scan-result { color: #ff6600; }
    </style>
</head>
<body>
    <h1>🗺️ H3 组件可视化测试</h1>
    
    <div class="info-panel">
        <h3>📊 测试概览</h3>
        <p><strong>测试中心:</strong> (34.0522, -118.2437)</p>
        <p><strong>测试半径:</strong> 8.0 km</p>
        <p><strong>生成时间:</strong> 2025-07-19 11:15:23</p>
    </div>
    
    <div class="controls">
        <button onclick="showResolution(5)">显示分辨率5 (红色)</button>
        <button onclick="showResolution(6)">显示分辨率6 (绿色)</button>
        <button onclick="showResolution(7)">显示分辨率7 (蓝色)</button>
        <button onclick="showScanResults()">显示扫描结果 (橙色)</button>
        <button onclick="showAll()">显示全部</button>
        <button onclick="clearAll()">清除全部</button>
    </div>
    
    <div id="map"></div>
    
    <div class="stats">
        <div class="stat-card">
            <h4>🔢 网格统计</h4>
            <ul>

                <li class="resolution-5">分辨率 5: 1 个网格, 总面积 282.31 km²</li>

                <li class="resolution-6">分辨率 6: 4 个网格, 总面积 161.40 km²</li>

                <li class="resolution-7">分辨率 7: 15 个网格, 总面积 86.48 km²</li>

            </ul>
        </div>
        
        <div class="stat-card">
            <h4>🔍 扫描统计</h4>
            <ul>
                <li>扫描网格数: 1</li>
                <li>API调用次数: 0</li>
                <li>唯一地点数: 0</li>
                <li>Mock模式: 是</li>
            </ul>
        </div>
        
        <div class="stat-card">
            <h4>📍 扫描结果详情</h4>
            <ul>

                <li>✅ 网格 8529a1d7...: 12 个地点 ⏹️</li>

            </ul>
        </div>
    </div>
    
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        // 初始化地图
        var map = L.map('map').setView([34.0522, -118.2437], 11);
        
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);
        
        // 添加中心点标记
        L.marker([34.0522, -118.2437])
            .addTo(map)
            .bindPopup('测试中心<br>半径: 8.0 km');
        
        // 添加半径圆圈
        L.circle([34.0522, -118.2437], {
            color: 'black',
            fillColor: 'transparent',
            fillOpacity: 0.1,
            radius: 8000.0
        }).addTo(map);
        
        // 存储图层
        var layers = {
            resolution5: L.layerGroup(),
            resolution6: L.layerGroup(),
            resolution7: L.layerGroup(),
            scanResults: L.layerGroup()
        };
        
        // 测试数据
        var testData = {
  "center": {
    "lat": 34.0522,
    "lng": -118.2437
  },
  "radius_km": 8.0,
  "resolutions": {
    "5": {
      "grid_count": 1,
      "grids": [
        {
          "grid_id": "8529a1d7fffffff",
          "center": {
            "lat": 34.10103463373918,
            "lng": -118.26966322581116
          },
          "boundary": [
            [
              34.0218042969173,
              -118.21157138802961
            ],
            [
              34.10486131297625,
              -118.15640747256164
            ],
            [
              34.18407900177168,
              -118.21451839922779
            ],
            [
              34.180180160385476,
              -118.32782169031148
            ],
            [
              34.09710606653405,
              -118.38285074576322
            ],
            [
              34.01794786850726,
              -118.32471169293866
            ]
          ],
          "area_km2": 282.30606201158946,
          "resolution": 5
        }
      ],
      "total_area_km2": 282.30606201158946
    },
    "6": {
      "grid_count": 4,
      "grids": [
        {
          "grid_id": "8629a1d77ffffff",
          "center": {
            "lat": 34.056317539680386,
            "lng": -118.22028837140907
          },
          "boundary": [
            [
              34.0218042969173,
              -118.21157138802961
            ],
            [
              34.04608060154378,
              -118.17964694303825
            ],
            [
              34.08059342486246,
              -118.18835725190152
            ],
            [
              34.090821995780715,
              -118.22901038984253
            ],
            [
              34.06653856035659,
              -118.26092950675842
            ],
            [
              34.032033689042564,
              -118.25220082760907
            ]
          ],
          "area_km2": 40.35892439206974,
          "resolution": 6
        },
        {
          "grid_id": "8629a1d47ffffff",
          "center": {
            "lat": 34.10103463373918,
            "lng": -118.26966322581116
          },
          "boundary": [
            [
              34.06653856035659,
              -118.26092950675842
            ],
            [
              34.090821995780715,
              -118.22901038984253
            ],
            [
              34.125317641141535,
              -118.23773744728918
            ],
            [
              34.135521885135255,
              -118.27840198872734
            ],
            [
              34.11123133362924,
              -118.3103157188798
            ],
            [
              34.0767436584327,
              -118.30157030819383
            ]
          ],
          "area_km2": 40.32948928717859,
          "resolution": 6
        },
        {
          "grid_id": "8629a1d67ffffff",
          "center": {
            "lat": 34.04224717405623,
            "lng": -118.29282994224529
          },
          "boundary": [
            [
              34.00774190456937,
              -118.28409461707679
            ],
            [
              34.032033689042564,
              -118.25220082760907
            ],
            [
              34.06653856035659,
              -118.26092950675842
            ],
            [
              34.0767436584327,
              -118.30157030819383
            ],
            [
              34.05244474684915,
              -118.33345869108281
            ],
            [
              34.01794786850726,
              -118.32471169293866
            ]
          ],
          "area_km2": 40.34309768346747,
          "resolution": 6
        },
        {
          "grid_id": "8629a56dfffffff",
          "center": {
            "lat": 33.99752004388834,
            "lng": -118.24347718440666
          },
          "boundary": [
            [
              33.96299764902136,
              -118.2347585731984
            ],
            [
              33.98728229160446,
              -118.20285943574866
            ],
            [
              34.0218042969173,
              -118.21157138802961
            ],
            [
              34.032033689042564,
              -118.25220082760907
            ],
            [
              34.00774190456937,
              -118.28409461707679
            ],
            [
              33.97322787407784,
              -118.27536432873468
            ]
          ],
          "area_km2": 40.37247799326683,
          "resolution": 6
        }
      ],
      "total_area_km2": 161.40398935598262
    },
    "7": {
      "grid_count": 15,
      "grids": [
        {
          "grid_id": "8729a1d75ffffff",
          "center": {
            "lat": 34.04390253163188,
            "lng": -118.2443312617128
          },
          "boundary": [
            [
              34.03258192893528,
              -118.23603417801797
            ],
            [
              34.04445016530797,
              -118.22816225703191
            ],
            [
              34.05577051319852,
              -118.2364597312414
            ],
            [
              34.05522140918642,
              -118.25262970463103
            ],
            [
              34.043352821471075,
              -118.26049887907232
            ],
            [
              34.032033689042564,
              -118.25220082760907
            ]
          ],
          "area_km2": 5.765194945881902,
          "resolution": 7
        },
        {
          "grid_id": "8729a1d74ffffff",
          "center": {
            "lat": 34.03312809279967,
            "lng": -118.21986614262346
          },
          "boundary": [
            [
              34.0218042969173,
              -118.21157138802961
            ],
            [
              34.03367218004759,
              -118.20369672382253
            ],
            [
              34.044995721910404,
              -118.21199186742669
            ],
            [
              34.04445016530797,
              -118.22816225703191
            ],
            [
              34.03258192893528,
              -118.23603417801797
            ],
            [
              34.021259602339754,
              -118.22773845356052
            ]
          ],
          "area_km2": 5.76671601693854,
          "resolution": 7
        },
        {
          "grid_id": "8729a1d70ffffff",
          "center": {
            "lat": 34.056317539680386,
            "lng": -118.22028837140907
          },
          "boundary": [
            [
              34.044995721910404,
              -118.21199186742669
            ],
            [
              34.05686248804333,
              -118.20411562753252
            ],
            [
              34.06818405042416,
              -118.21241252071142
            ],
            [
              34.067637632114135,
              -118.22858623575635
            ],
            [
              34.05577051319852,
              -118.2364597312414
            ],
            [
              34.04445016530797,
              -118.22816225703191
            ]
          ],
          "area_km2": 5.765561688434599,
          "resolution": 7
        },
        {
          "grid_id": "8729a1d71ffffff",
          "center": {
            "lat": 34.06708913522847,
            "lng": -118.24475856523819
          },
          "boundary": [
            [
              34.05577051319852,
              -118.2364597312414
            ],
            [
              34.067637632114135,
              -118.22858623575635
            ],
            [
              34.07895599796916,
              -118.23688546045496
            ],
            [
              34.07840603015583,
              -118.25305875900828
            ],
            [
              34.06653856035659,
              -118.26092950675842
            ],
            [
              34.05522140918642,
              -118.25262970463103
            ]
          ],
          "area_km2": 5.764039669996438,
          "resolution": 7
        },
        {
          "grid_id": "8729a1d62ffffff",
          "center": {
            "lat": 34.05467022823382,
            "lng": -118.2687982891808
          },
          "boundary": [
            [
              34.043352821471075,
              -118.26049887907232
            ],
            [
              34.05522140918642,
              -118.25262970463103
            ],
            [
              34.06653856035659,
              -118.26092950675842
            ],
            [
              34.06598590808902,
              -118.27709905791978
            ],
            [
              34.05411697093147,
              -118.28496548249475
            ],
            [
              34.04280103541551,
              -118.27666510671465
            ]
          ],
          "area_km2": 5.763671634177862,
          "resolution": 7
        },
        {
          "grid_id": "8729a1d66ffffff",
          "center": {
            "lat": 34.03148337371071,
            "lng": -118.26836608900103
          },
          "boundary": [
            [
              34.02016398705913,
              -118.26006842936876
            ],
            [
              34.032033689042564,
              -118.25220082760907
            ],
            [
              34.043352821471075,
              -118.26049887907232
            ],
            [
              34.04280103541551,
              -118.27666510671465
            ],
            [
              34.03093098352994,
              -118.28452995979941
            ],
            [
              34.019613067533875,
              -118.27623133485615
            ]
          ],
          "area_km2": 5.764825332022868,
          "resolution": 7
        },
        {
          "grid_id": "8729a56dbffffff",
          "center": {
            "lat": 34.02071283219091,
            "lng": -118.24390413480543
          },
          "boundary": [
            [
              34.00939025215961,
              -118.23560880067562
            ],
            [
              34.021259602339754,
              -118.22773845356052
            ],
            [
              34.03258192893528,
              -118.23603417801797
            ],
            [
              34.032033689042564,
              -118.25220082760907
            ],
            [
              34.02016398705913,
              -118.26006842936876
            ],
            [
              34.00884287670373,
              -118.25177212783251
            ]
          ],
          "area_km2": 5.766347696267135,
          "resolution": 7
        },
        {
          "grid_id": "8729a56daffffff",
          "center": {
            "lat": 34.0099355530894,
            "lng": -118.21944408835417
          },
          "boundary": [
            [
              33.99860978242904,
              -118.21115108241244
            ],
            [
              34.01047877890547,
              -118.20327799326367
            ],
            [
              34.0218042969173,
              -118.21157138802961
            ],
            [
              34.021259602339754,
              -118.22773845356052
            ],
            [
              34.00939025215961,
              -118.23560880067562
            ],
            [
              33.99806595019302,
              -118.2273148252336
            ]
          ],
          "area_km2": 5.767867817914977,
          "resolution": 7
        },
        {
          "grid_id": "8729a1d29ffffff",
          "center": {
            "lat": 34.02234691533618,
            "lng": -118.19540294060967
          },
          "boundary": [
            [
              34.01101992902127,
              -118.18711051780102
            ],
            [
              34.02288745701005,
              -118.17923311369896
            ],
            [
              34.03421419009194,
              -118.18752592401349
            ],
            [
              34.03367218004759,
              -118.20369672382253
            ],
            [
              34.0218042969173,
              -118.21157138802961
            ],
            [
              34.01047877890547,
              -118.20327799326367
            ]
          ],
          "area_km2": 5.768234843895506,
          "resolution": 7
        },
        {
          "grid_id": "8729a1d76ffffff",
          "center": {
            "lat": 34.045539200851294,
            "lng": -118.19582009529559
          },
          "boundary": [
            [
              34.03421419009194,
              -118.18752592401349
            ],
            [
              34.04608060154378,
              -118.17964694303825
            ],
            [
              34.05740535769975,
              -118.1879415020115
            ],
            [
              34.05686248804333,
              -118.20411562753252
            ],
            [
              34.044995721910404,
              -118.21199186742669
            ],
            [
              34.03367218004759,
              -118.20369672382253
            ]
          ],
          "area_km2": 5.767081464722336,
          "resolution": 7
        },
        {
          "grid_id": "8729a1d72ffffff",
          "center": {
            "lat": 34.06872838957006,
            "lng": -118.19623742250332
          },
          "boundary": [
            [
              34.05740535769975,
              -118.1879415020115
            ],
            [
              34.069270648964505,
              -118.18006094353314
            ],
            [
              34.080593424862464,
              -118.18835725190155
            ],
            [
              34.080049695911164,
              -118.204534704501
            ],
            [
              34.06818405042416,
              -118.21241252071142
            ],
            [
              34.05686248804333,
              -118.20411562753252
            ]
          ],
          "area_km2": 5.765925558747529,
          "resolution": 7
        },
        {
          "grid_id": "8729a1d73ffffff",
          "center": {
            "lat": 34.079503886750686,
            "lng": -118.22071077481921
          },
          "boundary": [
            [
              34.06818405042416,
              -118.21241252071142
            ],
            [
              34.080049695911164,
              -118.204534704501
            ],
            [
              34.091369275480304,
              -118.21283334799169
            ],
            [
              34.090821995780715,
              -118.22901038984253
            ],
            [
              34.07895599796916,
              -118.23688546045496
            ],
            [
              34.067637632114135,
              -118.22858623575635
            ]
          ],
          "area_km2": 5.764404835149547,
          "resolution": 7
        },
        {
          "grid_id": "8729a1d46ffffff",
          "center": {
            "lat": 34.09027263600379,
            "lng": -118.24518604549111
          },
          "boundary": [
            [
              34.07895599796916,
              -118.23688546045496
            ],
            [
              34.090821995780715,
              -118.22901038984253
            ],
            [
              34.102138376272954,
              -118.23731136576778
            ],
            [
              34.101587544977214,
              -118.25348799085081
            ],
            [
              34.08972119673949,
              -118.26136031253743
            ],
            [
              34.07840603015583,
              -118.25305875900828
            ]
          ],
          "area_km2": 5.762881871349495,
          "resolution": 7
        },
        {
          "grid_id": "8729a1d44ffffff",
          "center": {
            "lat": 34.077853983900944,
            "lng": -118.26923066808055
          },
          "boundary": [
            [
              34.06653856035659,
              -118.26092950675842
            ],
            [
              34.07840603015583,
              -118.25305875900828
            ],
            [
              34.08972119673949,
              -118.26136031253743
            ],
            [
              34.08916767857888,
              -118.27753318858271
            ],
            [
              34.077299859795794,
              -118.28540118527441
            ],
            [
              34.06598590808902,
              -118.27709905791978
            ]
          ],
          "area_km2": 5.762515412844892,
          "resolution": 7
        },
        {
          "grid_id": "8729a1d63ffffff",
          "center": {
            "lat": 34.06543117901732,
            "lng": -118.29326721632629
          },
          "boundary": [
            [
              34.05411697093147,
              -118.28496548249475
            ],
            [
              34.06598590808902,
              -118.27709905791978
            ],
            [
              34.077299859795794,
              -118.28540118527441
            ],
            [
              34.0767436584327,
              -118.30157030819383
            ],
            [
              34.06487437373406,
              -118.30943397958328
            ],
            [
              34.05356163787117,
              -118.30113128217842
            ]
          ],
          "area_km2": 5.762146085247888,
          "resolution": 7
        }
      ],
      "total_area_km2": 86.47741487359151
    }
  },
  "scan_results": [
    {
      "grid_id": "8529a1d7fffffff",
      "center": {
        "lat": 34.10103463373918,
        "lng": -118.26966322581116
      },
      "boundary": [
        [
          34.0218042969173,
          -118.21157138802961
        ],
        [
          34.10486131297625,
          -118.15640747256164
        ],
        [
          34.18407900177168,
          -118.21451839922779
        ],
        [
          34.180180160385476,
          -118.32782169031148
        ],
        [
          34.09710606653405,
          -118.38285074576322
        ],
        [
          34.01794786850726,
          -118.32471169293866
        ]
      ],
      "places_count": 12,
      "should_drill_down": false,
      "success": true,
      "places": [
        {
          "id": "MOCK_convenience_store_34.1010_-118.2697_0",
          "name": "Mock Convenience Store #1",
          "formattedAddress": "Mock Address 1, Test City",
          "location": {
            "latitude": 34.108030992990024,
            "longitude": -118.27924376205054
          },
          "types": [
            "convenience_store"
          ]
        },
        {
          "id": "MOCK_convenience_store_34.1010_-118.2697_1",
          "name": "Mock Convenience Store #2",
          "formattedAddress": "Mock Address 2, Test City",
          "location": {
            "latitude": 34.09113075956768,
            "longitude": -118.27479698278421
          },
          "types": [
            "convenience_store"
          ]
        },
        {
          "id": "MOCK_convenience_store_34.1010_-118.2697_2",
          "name": "Mock Convenience Store #3",
          "formattedAddress": "Mock Address 3, Test City",
          "location": {
            "latitude": 34.09652940663638,
            "longitude": -118.27300392778993
          },
          "types": [
            "convenience_store"
          ]
        }
      ],
      "metadata": {
        "api_calls": 2,
        "scan_time": "2025-07-19T11:15:23.654454",
        "grid_center": [
          34.10103463373918,
          -118.26966322581116
        ],
        "search_radius": 5000,
        "h3_resolution": 5,
        "level": 1,
        "place_types": [
          "convenience_store",
          "restaurant"
        ],
        "execution_time_seconds": 0.024350881576538086
      }
    }
  ],
  "scan_stats": {
    "api_calls_made": 0,
    "unique_places_found": 0,
    "mock_mode": true,
    "config": {
      "place_types": [
        "convenience_store",
        "restaurant"
      ],
      "layer_count": 3,
      "max_retries": 3,
      "timeout_seconds": 30
    }
  }
};
        
        // 创建网格图层
        function createGridLayers() {
            // 分辨率5 (红色)
            testData.resolutions[5].grids.forEach(function(grid) {
                var polygon = L.polygon(grid.boundary, {
                    color: '#ff0000',
                    weight: 2,
                    fillOpacity: 0.2
                }).bindPopup(
                    '<b>分辨率 5</b><br>' +
                    'Grid ID: ' + grid.grid_id + '<br>' +
                    '中心: (' + grid.center.lat.toFixed(4) + ', ' + grid.center.lng.toFixed(4) + ')<br>' +
                    '面积: ' + grid.area_km2.toFixed(2) + ' km²'
                );
                layers.resolution5.addLayer(polygon);
            });
            
            // 分辨率6 (绿色)
            testData.resolutions[6].grids.forEach(function(grid) {
                var polygon = L.polygon(grid.boundary, {
                    color: '#00ff00',
                    weight: 2,
                    fillOpacity: 0.2
                }).bindPopup(
                    '<b>分辨率 6</b><br>' +
                    'Grid ID: ' + grid.grid_id + '<br>' +
                    '中心: (' + grid.center.lat.toFixed(4) + ', ' + grid.center.lng.toFixed(4) + ')<br>' +
                    '面积: ' + grid.area_km2.toFixed(2) + ' km²'
                );
                layers.resolution6.addLayer(polygon);
            });
            
            // 分辨率7 (蓝色)
            testData.resolutions[7].grids.forEach(function(grid) {
                var polygon = L.polygon(grid.boundary, {
                    color: '#0000ff',
                    weight: 2,
                    fillOpacity: 0.2
                }).bindPopup(
                    '<b>分辨率 7</b><br>' +
                    'Grid ID: ' + grid.grid_id + '<br>' +
                    '中心: (' + grid.center.lat.toFixed(4) + ', ' + grid.center.lng.toFixed(4) + ')<br>' +
                    '面积: ' + grid.area_km2.toFixed(2) + ' km²'
                );
                layers.resolution7.addLayer(polygon);
            });
            
            // 扫描结果 (橙色)
            testData.scan_results.forEach(function(result) {
                var polygon = L.polygon(result.boundary, {
                    color: '#ff6600',
                    weight: 3,
                    fillOpacity: 0.3
                });
                
                var popupContent = '<b>扫描结果</b><br>' +
                    'Grid ID: ' + result.grid_id + '<br>' +
                    '地点数量: ' + result.places_count + '<br>' +
                    '需要钻入: ' + (result.should_drill_down ? '是' : '否') + '<br>' +
                    '成功: ' + (result.success ? '是' : '否') + '<br>';
                
                if (result.places.length > 0) {
                    popupContent += '<br><b>示例地点:</b><br>';
                    result.places.forEach(function(place, index) {
                        popupContent += (index + 1) + '. ' + place.name + '<br>';
                    });
                }
                
                polygon.bindPopup(popupContent);
                layers.scanResults.addLayer(polygon);
            });
        }
        
        // 控制函数
        function showResolution(res) {
            clearAll();
            layers['resolution' + res].addTo(map);
        }
        
        function showScanResults() {
            clearAll();
            layers.scanResults.addTo(map);
        }
        
        function showAll() {
            Object.values(layers).forEach(layer => layer.addTo(map));
        }
        
        function clearAll() {
            Object.values(layers).forEach(layer => map.removeLayer(layer));
        }
        
        // 初始化
        createGridLayers();
        showAll();
    </script>
</body>
</html>
