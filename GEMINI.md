# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## High-Level Code Architecture and Structure

The `grid-map-scanner` project implements an adaptive, hierarchical grid scanning system for discovering Points of Interest (POIs) using the Google Places API. The codebase is evolving from a simpler script-based approach to a more modular and project-oriented architecture.

*   **`ProjectManager` (`src/project_manager.py`):** Central to the new architecture, this module manages project creation, configuration, and the state of H3 grid cells across various resolution levels. It handles persistent storage of project configurations and grid statuses, and orchestrates the initial generation of Level 1 grids as well as the dynamic generation of finer-grained child grids based on scan results.

*   **Grid Generation (`src/grid_generator.py`):** This module is dedicated to creating H3 hexagonal grids according to specified resolutions and geographical areas. It supports the generation of macro, fine, and enhanced level grids, and can dynamically produce child grids as needed during the scanning process.

*   **Google Places API Client (`src/places_client.py`):** This client manages all interactions with the Google Places API. It incorporates robust error handling, an exponential backoff retry mechanism, and client-side rate limiting to prevent exceeding API quotas. A mock mode is available for development and testing to avoid actual API costs. The client is also responsible for transforming raw API responses into structured `PlaceData` objects.

*   **Configuration (`src/config.py`):** This file defines all adjustable parameters for the scanning operation, including grid dimensions, search radii, recursion thresholds, API budget controls, and specific API request parameters. It also includes built-in validation logic for these configuration settings.

*   **Data Models (`src/models.py`):** Contains dataclass definitions for key data structures used throughout the application, such as `Coordinate`, `GridPoint`, `Area`, `PlaceData`, `ScanSession`, and `ScanResult`. These models ensure consistent and organized data representation.

*   **Legacy Scanner (`src/adaptive_grid_scan.py`):** This script represents an earlier, monolithic implementation of the adaptive scanning logic. While still part of the repository, the development direction is towards leveraging the `ProjectManager` and other modular components for enhanced scalability and maintainability. It serves as a reference for the foundational scanning methodology.

## Commonly Used Commands

### Running Tests

The project utilizes `pytest` for its test suite.

```bash
GOOGLE_PLACES_API_KEY="test_key" python -m pytest src/tests/ -v
```
This command executes all tests located in the `src/tests/` directory with verbose output. The `GOOGLE_PLACES_API_KEY="test_key"` prefix is essential for enabling the mock API mode within tests, allowing them to run without a real Google Places API key.

### Running the Application

To run the `ProjectManager` example:

```bash
python src/project_manager.py
```

To run the legacy `adaptive_grid_scan.py` script in mock mode:

```bash
python src/adaptive_grid_scan.py --mock
```

To perform a real scan using `adaptive_grid_scan.py` (requires the `GOOGLE_PLACES_API_KEY` environment variable to be set with a valid API key):

```bash
python src/adaptive_grid_scan.py --save-data --visualize --map --output-dir ./scan_outputs
```