#!/usr/bin/env python3
"""
验证H3网格搜索半径重叠情况
"""

import json
import h3
import math
from pathlib import Path
from geopy.distance import geodesic

def calculate_distance(lat1, lon1, lat2, lon2):
    """计算两点间距离（米）"""
    return geodesic((lat1, lon1), (lat2, lon2)).meters

def get_h3_geometry(h3_resolution):
    """获取H3网格几何特性"""
    try:
        edge_length_km = h3.edge_length(h3_resolution, 'km')
    except:
        edge_length_approx = {5: 8.5, 6: 3.2, 7: 1.2, 8: 0.46, 9: 0.17}
        edge_length_km = edge_length_approx.get(h3_resolution, 1.0)
    
    return {
        'edge_length_km': edge_length_km,
        'circumradius_km': edge_length_km,  # 外接圆半径 = 边长
        'inradius_km': edge_length_km * 0.866,  # 内切圆半径
    }

def verify_project_overlap(project_name):
    """验证项目的网格重叠情况"""
    
    print(f"🔍 验证项目 '{project_name}' 的网格重叠情况")
    print("=" * 60)
    
    # 加载项目配置
    config_file = Path("projects") / project_name / "project_config.json"
    if not config_file.exists():
        print(f"❌ 项目 {project_name} 不存在")
        return
        
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 加载Level 5网格
    grids_file = Path("projects") / project_name / "grids" / "level5_grids.json"
    if not grids_file.exists():
        print(f"❌ 网格文件不存在")
        return
        
    with open(grids_file, 'r', encoding='utf-8') as f:
        grids = json.load(f)
    
    print(f"📊 项目配置:")
    print(f"   Level 1搜索半径: {config['search_radius_level1']}m ({config['search_radius_level1']/1000:.2f}km)")
    print(f"   Level 2搜索半径: {config['search_radius_level2']}m ({config['search_radius_level2']/1000:.2f}km)")
    print(f"   Level 3搜索半径: {config['search_radius_level3']}m ({config['search_radius_level3']/1000:.2f}km)")
    
    # 分析Level 5网格
    h3_res = config['h3_res_level1']
    search_radius_m = config['search_radius_level1']
    geometry = get_h3_geometry(h3_res)
    
    print(f"\n📐 H3 res {h3_res} 几何特性:")
    print(f"   边长: {geometry['edge_length_km']:.2f}km")
    print(f"   外接圆半径: {geometry['circumradius_km']:.2f}km")
    print(f"   内切圆半径: {geometry['inradius_km']:.2f}km")
    print(f"   配置的搜索半径: {search_radius_m/1000:.2f}km")
    
    # 计算相邻网格距离
    print(f"\n🔗 相邻网格距离分析:")
    grid_centers = [(g['latitude'], g['longitude'], g['h3_index']) for g in grids]
    
    min_distance = float('inf')
    max_distance = 0
    distances = []
    
    for i, (lat1, lon1, h3_1) in enumerate(grid_centers):
        for j, (lat2, lon2, h3_2) in enumerate(grid_centers):
            if i >= j:
                continue
                
            distance_m = calculate_distance(lat1, lon1, lat2, lon2)
            distances.append(distance_m)
            
            # 检查是否为相邻网格
            if h3.are_neighbor_cells(h3_1, h3_2):
                print(f"   相邻网格距离: {distance_m:.0f}m ({distance_m/1000:.2f}km)")
                min_distance = min(min_distance, distance_m)
                max_distance = max(max_distance, distance_m)
    
    if min_distance != float('inf'):
        print(f"\n📏 相邻网格距离统计:")
        print(f"   最小距离: {min_distance:.0f}m ({min_distance/1000:.2f}km)")
        print(f"   最大距离: {max_distance:.0f}m ({max_distance/1000:.2f}km)")
        
        # 计算重叠情况
        overlap_distance = 2 * search_radius_m - min_distance
        coverage_gap = min_distance - 2 * search_radius_m
        
        print(f"\n🎯 重叠分析:")
        print(f"   搜索半径: {search_radius_m:.0f}m")
        print(f"   两个搜索圆的理论覆盖距离: {2 * search_radius_m:.0f}m")
        print(f"   实际相邻网格距离: {min_distance:.0f}m")
        
        if overlap_distance > 0:
            print(f"   ✅ 重叠距离: {overlap_distance:.0f}m ({overlap_distance/1000:.3f}km)")
            print(f"   重叠比例: {overlap_distance/min_distance*100:.1f}%")
        else:
            print(f"   ❌ 覆盖空隙: {abs(coverage_gap):.0f}m ({abs(coverage_gap)/1000:.3f}km)")
    
    print(f"\n💡 建议:")
    theoretical_radius = geometry['circumradius_km'] * 1000
    print(f"   理论最小搜索半径（外接圆）: {theoretical_radius:.0f}m")
    print(f"   建议搜索半径（1.15倍外接圆）: {theoretical_radius * 1.15:.0f}m")
    print(f"   当前搜索半径: {search_radius_m:.0f}m")
    
    if search_radius_m >= theoretical_radius * 1.15:
        print(f"   ✅ 搜索半径设置合理，应该有适当重叠")
    elif search_radius_m >= theoretical_radius:
        print(f"   ⚠️  搜索半径刚好覆盖，可能重叠不足")
    else:
        print(f"   ❌ 搜索半径过小，可能有覆盖空隙")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        project_name = sys.argv[1]
    else:
        project_name = "test-correct-radius"
    
    verify_project_overlap(project_name)
