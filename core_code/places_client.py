#!/usr/bin/env python
# places_client.py
# Author: <PERSON><PERSON><PERSON><PERSON><PERSON>@chatgpt
# Description: Google Places API 客户端，支持 mock 和真实请求

import os
import json
import random
import time
from typing import List, Dict, Optional
from dotenv import load_dotenv

# 可选导入
try:
    import requests
except ImportError:
    requests = None

# 加载环境变量
load_dotenv()

class PlacesClient:
    """Google Places API 客户端"""
    
    def __init__(self, mock: bool = False):
        self.mock = mock
        self.api_key = os.getenv("GOOGLE_API_KEY")
        self.field_mask = (
            "places.id,places.name,places.formattedAddress,"
            "places.location,places.types"
        )
        
        if not mock and not self.api_key:
            raise ValueError("真实请求模式需要设置 GOOGLE_API_KEY 环境变量")
        
        if not mock and requests is None:
            raise ValueError("真实请求模式需要安装 requests: pip install requests")
    
    def search_nearby(
        self, 
        lat: float, 
        lon: float, 
        radius: int, 
        place_type: str
    ) -> List[Dict]:
        """
        同步版本的附近搜索
        
        Args:
            lat: 纬度
            lon: 经度  
            radius: 搜索半径(米)
            place_type: 地点类型
            
        Returns:
            地点列表
        """
        if self.mock:
            return self._mock_search(lat, lon, radius, place_type)
        else:
            return self._real_search(lat, lon, radius, place_type)
    
    def _mock_search(
        self, 
        lat: float, 
        lon: float, 
        radius: int, 
        place_type: str
    ) -> List[Dict]:
        """生成模拟数据"""
        # 添加小延时模拟网络请求
        time.sleep(0.01)
        
        # 随机生成 0-25 个结果，最多20个
        count = min(random.randint(0, 25), 20)
        
        places = []
        for i in range(count):
            place = {
                "id": f"MOCK_{place_type}_{lat:.4f}_{lon:.4f}_{i}",
                "name": f"Mock {place_type.replace('_', ' ').title()} #{i+1}",
                "formattedAddress": f"Mock Address {i+1}, Test City",
                "location": {
                    "latitude": lat + random.uniform(-0.01, 0.01),
                    "longitude": lon + random.uniform(-0.01, 0.01)
                },
                "types": [place_type]
            }
            places.append(place)
        
        return places
    
    def _real_search(
        self, 
        lat: float, 
        lon: float, 
        radius: int, 
        place_type: str,
        max_retries: int = 3
    ) -> List[Dict]:
        """发送真实的 API 请求"""
        
        url = "https://places.googleapis.com/v1/places:searchNearby"
        headers = {
            "X-Goog-Api-Key": self.api_key,
            "X-Goog-FieldMask": self.field_mask,
            "Content-Type": "application/json"
        }
        payload = {
            "includedTypes": [place_type],
            "maxResultCount": 20,
            "locationRestriction": {
                "circle": {
                    "center": {
                        "latitude": lat,
                        "longitude": lon
                    },
                    "radius": radius
                }
            }
        }
        
        for attempt in range(1, max_retries + 1):
            try:
                response = requests.post(
                    url, 
                    headers=headers, 
                    json=payload, 
                    timeout=15
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return data.get("places", [])
                else:
                    error_msg = f"HTTP {response.status_code}: {response.text[:120]}"
                    if attempt == max_retries:
                        print(f"❌ API 请求失败: {error_msg}")
                        return []
                    else:
                        print(f"⚠️  重试 {attempt}/{max_retries}: {error_msg}")
                        time.sleep(1.5 * attempt)
                        
            except Exception as e:
                if attempt == max_retries:
                    print(f"❌ 请求异常: {e}")
                    return []
                else:
                    print(f"⚠️  重试 {attempt}/{max_retries}: {e}")
                    time.sleep(1.5 * attempt)
        
        return []
    
    def get_stats(self) -> Dict:
        """获取客户端统计信息"""
        return {
            "mode": "mock" if self.mock else "real",
            "api_key_configured": bool(self.api_key)
        }


# 简单的使用示例
if __name__ == "__main__":
    # Mock 模式测试
    client = PlacesClient(mock=True)
    result = client.search_nearby(34.0522, -118.2437, 1000, "convenience_store")
    print(f"Mock 结果数量: {len(result)}")
    if result:
        print(f"示例: {json.dumps(result[0], indent=2)}") 