#!/usr/bin/env python
# adaptive_grid_scan.py
# Author: m<PERSON><PERSON><PERSON><PERSON>@chatgpt (revised v4)
# Description: Adaptive hierarchical grid scanning with Google Places API,
# with optional --mock flag to avoid real API cost during testing.
# Simplified version without concurrency.

import os
import math
import json
import time
import argparse
from typing import List, Dict, Set, Tuple, Optional

# Optional imports
try:
    from shapely.geometry import Point, Polygon  # type: ignore
except ImportError:
    Point = None  # type: ignore
    Polygon = None  # type: ignore

import h3
from h3 import LatLngPoly
from tqdm import tqdm
from places_client import PlacesClient

# ──────────────────────────── 配置区 ──────────────────────────── #

# 1. 搜索类型
PLACE_TYPES = ["convenience_store"]          # 可扩充为多元素列表

# 2. 三层扫描参数（可按照城市密度调节）
LAYER_CFG = [
    dict(h3_res=5, search_radius=5_000),   # 粗筛：红圈
    dict(h3_res=7, search_radius=1_000),   # 精筛：黄圈
    dict(h3_res=8, search_radius=500),     # 增强：绿圈
]

# 3. 根区域：中心点 + 半径 (km)
ROOT_CENTER = (34.0522, -118.2437)          # 洛杉矶市中心
ROOT_RADIUS_KM = 96.6                       # 60 英里

# ──────────────────────────── 工具函数 ──────────────────────────── #

def _deg_offset(lat: float, dx_m: float, dy_m: float) -> Tuple[float, float]:
    new_lat = lat + (dy_m / 1000) / 111.0
    new_lon = 0.0
    if abs(lat) < 89.999:
        new_lon = dx_m / (1000 * 111.0 * math.cos(math.radians(lat)))
    return new_lat, new_lon


def make_circle_polygon(
    center_latlon: Tuple[float, float], radius_km: float, n_points: int = 36
):
    lat, lon = center_latlon
    coords = []
    for i in range(n_points):
        angle = 2 * math.pi * i / n_points
        dx = radius_km * 1000 * math.cos(angle)
        dy = radius_km * 1000 * math.sin(angle)
        dlat, dlon = _deg_offset(lat, dx, dy)
        coords.append((dlat, lon + dlon))  # h3.LatLngPoly expects (lat, lon)
    
    # Always return h3.LatLngPoly for h3 v4+ compatibility
    return LatLngPoly(coords)

# ──────────────────────────── 核心扫描逻辑 ──────────────────────────── #

class AdaptiveScanner:
    def __init__(self, mock: bool = False):
        self.visited_place_ids: Set[str] = set()
        self.api_calls = 0
        self.places_client = PlacesClient(mock=mock)
        self.layer_data = []  # 记录每层数据

    def scan_layer(
        self,
        hexes: List[str],
        layer_idx: int,
        layer_config: Dict, # 直接接收层级配置
        place_type: str,
    ) -> Tuple[List[str], List[Dict]]:
        """扫描单个层级的所有六边形"""
        search_r = layer_config["search_radius"]
        next_hexes: List[str] = []
        hex_results = []  # 记录每个网格的结果

        print(f"  处理 {len(hexes)} 个网格...")
        layer_api_calls = 0
        layer_places_found = 0
        
        for h in tqdm(hexes, desc=f"Layer {layer_idx} (res={layer_config['h3_res']}, r={search_r}m)"):
            lat, lon = h3.cell_to_latlng(h)
            places = self.places_client.search_nearby(lat, lon, search_r, place_type)
            self.api_calls += 1
            layer_api_calls += 1
            
            new_ids = {p["id"] for p in places}
            new_places_count = len(new_ids - self.visited_place_ids)  # 实际新发现的POI
            self.visited_place_ids.update(new_ids)
            layer_places_found += new_places_count
            
            # 记录这个网格的详细信息
            hex_info = {
                "hex_id": h,
                "lat": lat,
                "lon": lon,
                "places_count": len(places),
                "new_places_count": new_places_count,
                "places": places if len(places) <= 5 else places[:5],  # 只保存前5个作为示例
                "is_full": len(places) == 20
            }
            hex_results.append(hex_info)
            
            # 如果找到满额结果，则需要细分
            if len(places) == 20:
                next_hexes.append(h)

        # 记录这一层的统计信息
        layer_stats = {
            "layer_index": layer_idx,
            "h3_resolution": layer_config["h3_res"],
            "search_radius": search_r,
            "hex_count": len(hexes),
            "api_calls": layer_api_calls,
            "total_places_found": layer_places_found,
            "full_hexes_count": len(next_hexes),
            "hexes": hex_results
        }
        self.layer_data.append(layer_stats)

        return next_hexes, hex_results

    def run(self):
        """执行自适应扫描的核心逻辑"""
        root_shape = make_circle_polygon(ROOT_CENTER, ROOT_RADIUS_KM)
        results_by_type = {}

        for place_type in PLACE_TYPES:
            print(f"\n=== 扫描类型: {place_type} | mock={self.places_client.mock} ===")
            
            # 为每个类型重置状态
            self.visited_place_ids.clear()
            self.layer_data.clear()
            self.api_calls = 0
            
            # 1. 初始化：从第0层配置开始
            layer_idx = 0
            current_config = LAYER_CFG[0]
            hexes_to_scan = list(h3.polygon_to_cells(root_shape, current_config["h3_res"]))

            # 2. 循环扫描：只要有待处理的网格就继续
            while hexes_to_scan:
                print(f"\n· Layer {layer_idx} | h3_res={current_config['h3_res']} | radius={current_config['search_radius']}m | hex count={len(hexes_to_scan)}")
                
                # 执行单层扫描
                full_hexes, _ = self.scan_layer(
                    hexes=hexes_to_scan,
                    layer_idx=layer_idx,
                    layer_config=current_config,
                    place_type=place_type
                )

                # 如果没有满额的网格，说明扫描完成
                if not full_hexes:
                    break

                # 3. 准备下一层
                layer_idx += 1
                next_h3_res = -1
                
                # 3.1. 配置驱动：优先使用 LAYER_CFG 中预设的下一层配置
                if layer_idx < len(LAYER_CFG):
                    print(f"  使用预设配置生成 Layer {layer_idx}...")
                    next_config = LAYER_CFG[layer_idx]
                
                # 3.2. 动态扩展：如果预设配置用完，则动态生成新层级
                else:
                    # 停止条件：检查上一层搜索半径是否已小于等于250米
                    if current_config["search_radius"] <= 250:
                        print(f"  上一层搜索半径 ({current_config['search_radius']}m) 已达最小阈值，停止深化。")
                        break
                    
                    print(f"  动态生成 Layer {layer_idx}...")
                    # 动态计算下一层参数：分辨率+1，半径减半
                    next_config = {
                        "h3_res": current_config["h3_res"] + 1,
                        "search_radius": math.ceil(current_config["search_radius"] / 2)
                    }

                # 3.3. 生成子网格
                child_hexes = []
                print(f"  生成子网格 (h3_res={next_config['h3_res']})...")
                for h in tqdm(full_hexes, desc="生成子网格"):
                    child_hexes.extend(h3.cell_to_children(h, next_config['h3_res']))
                
                hexes_to_scan = list(set(child_hexes))
                current_config = next_config
            
            results_by_type[place_type] = len(self.visited_place_ids)

        return results_by_type

    def save_detailed_results(self, filename: str = "scan_results.json"):
        """保存详细的扫描结果"""
        detailed_results = {
            "scan_info": {
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "mock_mode": self.places_client.mock,
                "place_types": PLACE_TYPES,
                "root_center": ROOT_CENTER,
                "root_radius_km": ROOT_RADIUS_KM,
                "layer_config": LAYER_CFG
            },
            "summary": {
                "total_api_calls": self.api_calls,
                "total_unique_places": len(self.visited_place_ids),
                "total_layers": len(self.layer_data),
                "cost_estimate": self.api_calls * (0.0 if self.places_client.mock else 0.032)
            },
            "layers": self.layer_data
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(detailed_results, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 详细结果已保存: {filename}")
        return detailed_results

# ──────────────────────────── CLI ──────────────────────────── #

def parse_args():
    p = argparse.ArgumentParser(description="Adaptive grid scanner for Google Places API")
    p.add_argument("--mock", action="store_true", help="Run in mock mode with fake Places data")
    p.add_argument("--save-data", action="store_true", help="Save detailed scan results to JSON file")
    p.add_argument("--visualize", action="store_true", help="Generate visualization plots")
    p.add_argument("--map", action="store_true", help="Generate interactive map visualization")
    p.add_argument("--output-dir", default=".", help="Output directory for files")
    return p.parse_args()

if __name__ == "__main__":
    args = parse_args()
    t0 = time.time()
    
    print("🚀 开始自适应网格扫描...")
    scanner = AdaptiveScanner(mock=args.mock)
    result = scanner.run()

    unit_cost = 0.0 if args.mock else 0.032
    cost = scanner.api_calls * unit_cost

    print("\n\n===== 扫描完成 =====")
    print(f"总调用次数: {scanner.api_calls}")
    print(f"累计发现 place_id: {len(scanner.visited_place_ids)}")
    print(f"预估成本: ${cost:,.2f}")
    print(f"用时: {time.time() - t0:.1f}s")
    print(f"按类型汇总: {json.dumps(result, indent=2)}")
    
    # 保存详细数据
    if args.save_data or args.visualize or args.map:
        results_file = os.path.join(args.output_dir, "scan_results.json")
        detailed_data = scanner.save_detailed_results(results_file)
        
        # 生成可视化
        if args.visualize:
            try:
                from visualizer import ScanResultVisualizer
                
                print("\n📊 生成可视化图表...")
                visualizer = ScanResultVisualizer(detailed_data)
                
                # 生成概览图
                overview_path = os.path.join(args.output_dir, "scan_overview.png")
                visualizer.plot_overview(overview_path)
                
                # 为每一层生成详细图
                for i in range(len(detailed_data['layers'])):
                    detail_path = os.path.join(args.output_dir, f"layer_{i}_detail.png")
                    visualizer.plot_layer_detail(i, detail_path)
                
                # 导出摘要
                summary_path = os.path.join(args.output_dir, "scan_summary.json")
                visualizer.export_summary(summary_path)
                
                print(f"📊 可视化完成！文件保存在: {args.output_dir}")
                
            except ImportError as e:
                print(f"⚠️  可视化功能需要额外依赖: {e}")
                print("请运行: pip install matplotlib numpy")
            except Exception as e:
                print(f"❌ 可视化生成失败: {e}")
        
        # 生成交互式地图
        if args.map:
            try:
                from map_visualizer import InteractiveMapVisualizer
                
                print("\n🗺️  生成交互式地图...")
                map_viz = InteractiveMapVisualizer(detailed_data)
                
                # 创建主地图
                main_map_path = os.path.join(args.output_dir, "scan_map.html")
                map_viz.create_interactive_map(main_map_path)
                
                # 创建层级对比地图
                comparison_map_path = os.path.join(args.output_dir, "layer_comparison.html")
                map_viz.create_layer_comparison_map(comparison_map_path)
                
                # 导出GeoJSON
                geojson_path = os.path.join(args.output_dir, "scan_results.geojson")
                map_viz.export_geojson(geojson_path)
                
                print(f"🗺️  地图可视化完成！")
                print(f"📁 打开 {main_map_path} 查看交互式地图")
                
            except ImportError as e:
                print(f"⚠️  地图可视化功能需要额外依赖: {e}")
                print("请运行: pip install folium")
            except Exception as e:
                print(f"❌ 地图可视化生成失败: {e}")
    
    print(f"\n🎉 扫描任务完成！")
    if not (args.save_data or args.visualize or args.map):
        print("💡 提示: 使用 --save-data 保存详细数据，--visualize 生成图表，--map 生成交互式地图")