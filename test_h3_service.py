#!/usr/bin/env python3
"""
测试 H3 Service Layer 的功能
"""

import sys
import os

# 添加 src 目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

import h3
from h3_service import H3Converter, H3Visualizer, H3ScannerBridge
from h3_service.h3_scanner_bridge import ScanConfig, GridSpec


def test_h3_converter():
    """测试 H3Converter 功能"""
    print("🧪 测试 H3Converter...")
    
    # 测试区域转网格
    center_lat, center_lng = 34.0522, -118.2437  # 洛杉矶
    radius_km = 5.0
    h3_resolution = 7
    
    grids = H3Converter.area_to_grids(center_lat, center_lng, radius_km, h3_resolution)
    print(f"✅ 区域转网格: 半径{radius_km}km, 分辨率{h3_resolution}, 生成{len(grids)}个网格")
    
    # 测试网格转中心坐标
    if grids:
        test_grid = grids[0]
        lat, lng = H3Converter.grid_to_center(test_grid)
        print(f"✅ 网格转坐标: {test_grid} -> ({lat:.4f}, {lng:.4f})")
        
        # 测试获取网格信息
        grid_info = H3Converter.get_grid_info(test_grid)
        print(f"✅ 网格信息: 分辨率{grid_info['resolution']}, 面积{grid_info['area_km2']:.2f}km²")
        
        # 测试生成子网格
        if grid_info['resolution'] < 10:
            children = H3Converter.generate_children(test_grid, grid_info['resolution'] + 1)
            print(f"✅ 生成子网格: {len(children)}个子网格")
    
    # 测试分辨率信息
    res_info = H3Converter.get_resolution_info(7)
    print(f"✅ 分辨率7信息: 平均面积{res_info['average_area_km2']:.2f}km²")
    
    return True


def test_h3_visualizer():
    """测试 H3Visualizer 功能"""
    print("\n🧪 测试 H3Visualizer...")
    
    # 创建测试网格
    center_lat, center_lng = 34.0522, -118.2437
    test_grid = h3.latlng_to_cell(center_lat, center_lng, 7)
    
    # 测试获取边界
    boundary = H3Visualizer.get_grid_boundary(test_grid)
    print(f"✅ 网格边界: {len(boundary)}个顶点")
    
    # 测试覆盖范围信息
    coverage_info = H3Visualizer.get_grid_coverage_info(test_grid)
    print(f"✅ 覆盖范围: 中心({coverage_info['center']['lat']:.4f}, {coverage_info['center']['lng']:.4f})")
    
    # 测试GeoJSON
    geojson = H3Visualizer.get_grid_geojson(test_grid)
    print(f"✅ GeoJSON: 类型{geojson['type']}, 几何类型{geojson['geometry']['type']}")
    
    # 测试多网格覆盖
    grids = H3Converter.area_to_grids(center_lat, center_lng, 2.0, 7)[:5]  # 取前5个
    if len(grids) > 1:
        multi_coverage = H3Visualizer.get_multiple_grids_coverage(grids)
        print(f"✅ 多网格覆盖: {multi_coverage['grid_count']}个网格, 总面积{multi_coverage['total_area_km2']:.2f}km²")
        
        # 测试缩放级别计算
        zoom_level = H3Visualizer.calculate_optimal_zoom_level(grids)
        print(f"✅ 最佳缩放级别: {zoom_level}")
    
    return True


def test_h3_scanner_bridge():
    """测试 H3ScannerBridge 功能"""
    print("\n🧪 测试 H3ScannerBridge...")
    
    # 创建配置
    config = H3ScannerBridge.create_default_config(
        place_types=["convenience_store"],
        mock_mode=True
    )
    print(f"✅ 创建默认配置: {len(config.place_types)}个地点类型, {len(config.layer_config)}层配置")
    
    # 创建桥接器
    bridge = H3ScannerBridge(config)
    print("✅ 创建扫描桥接器")
    
    # 测试单网格扫描
    center_lat, center_lng = 34.0522, -118.2437
    test_grid = h3.latlng_to_cell(center_lat, center_lng, 5)  # 使用分辨率5匹配Level 1
    
    result = bridge.scan_single_grid(test_grid, level=1)
    print(f"✅ 单网格扫描: 成功={result.success}, 找到{len(result.places)}个地点")
    print(f"   是否需要钻入下一层: {result.should_drill_down}")
    print(f"   API调用次数: {result.metadata.get('api_calls', 0)}")
    
    # 测试批量扫描
    grids = H3Converter.area_to_grids(center_lat, center_lng, 3.0, 5)[:3]  # 取前3个
    grid_specs = [GridSpec(grid_id=grid, level=1) for grid in grids]
    
    batch_results = bridge.scan_multiple_grids(grid_specs)
    print(f"✅ 批量扫描: {len(batch_results)}个网格")
    
    successful_scans = sum(1 for r in batch_results if r.success)
    total_places = sum(len(r.places) for r in batch_results)
    print(f"   成功扫描: {successful_scans}/{len(batch_results)}")
    print(f"   总共找到: {total_places}个地点")
    
    # 测试统计信息
    stats = bridge.get_scanner_stats()
    print(f"✅ 扫描统计: API调用{stats['api_calls_made']}次, 唯一地点{stats['unique_places_found']}个")
    
    # 测试网格验证
    is_valid = bridge.validate_grid_for_level(test_grid, 1)
    print(f"✅ 网格验证: 网格{test_grid}适合Level 1: {is_valid}")
    
    return True


def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试错误处理...")
    
    # 测试无效的H3 ID
    try:
        H3Converter.grid_to_center("invalid_h3_id")
        print("❌ 应该抛出无效H3 ID错误")
        return False
    except ValueError as e:
        print(f"✅ 正确捕获无效H3 ID错误: {e}")
    
    # 测试无效的分辨率
    try:
        H3Converter.get_resolution_info(20)
        print("❌ 应该抛出无效分辨率错误")
        return False
    except ValueError as e:
        print(f"✅ 正确捕获无效分辨率错误: {e}")
    
    # 测试无效的配置
    try:
        invalid_config = ScanConfig(
            place_types=[],  # 空列表
            layer_config=[{"h3_res": 5, "search_radius": 5000}],
            mock_mode=True
        )
        H3ScannerBridge(invalid_config)
        print("❌ 应该抛出无效配置错误")
        return False
    except ValueError as e:
        print(f"✅ 正确捕获无效配置错误: {e}")
    
    return True


def main():
    """主测试函数"""
    print("🚀 开始测试 H3 Service Layer...")
    
    tests = [
        test_h3_converter,
        test_h3_visualizer,
        test_h3_scanner_bridge,
        test_error_handling
    ]
    
    passed = 0
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ 测试 {test.__name__} 失败")
        except Exception as e:
            print(f"❌ 测试 {test.__name__} 异常: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n📊 测试结果: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("🎉 所有测试通过！")
        return True
    else:
        print("💥 部分测试失败！")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
