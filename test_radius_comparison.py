#!/usr/bin/env python3
"""
搜索半径对比测试
比较修复前后的搜索半径设置
"""

import json
from pathlib import Path

def compare_search_radius():
    """比较不同项目的搜索半径设置"""
    
    projects_dir = Path("projects")
    
    # 要比较的项目
    projects_to_compare = [
        "test-fixed",      # 修复前的项目
        "test-overlap",    # 第一次修复
        "test-fixed-radius"  # 最终修复
    ]
    
    print("🔍 搜索半径对比分析")
    print("=" * 60)
    
    for project_name in projects_to_compare:
        config_file = projects_dir / project_name / "project_config.json"
        
        if not config_file.exists():
            print(f"❌ 项目 {project_name} 不存在")
            continue
            
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"\n📁 项目: {project_name}")
        print(f"   Level 1 (H3 res {config['h3_res_level1']}): {config['search_radius_level1']}m ({config['search_radius_level1']/1000:.1f}km)")
        print(f"   Level 2 (H3 res {config['h3_res_level2']}): {config['search_radius_level2']}m ({config['search_radius_level2']/1000:.1f}km)")
        print(f"   Level 3 (H3 res {config['h3_res_level3']}): {config['search_radius_level3']}m ({config['search_radius_level3']/1000:.1f}km)")
    
    print("\n" + "=" * 60)
    print("📊 H3网格理论几何特性:")
    print("   H3 res 5: 边长8.5km, 外接圆半径8.5km, 内切圆半径7.4km")
    print("   H3 res 7: 边长1.2km, 外接圆半径1.2km, 内切圆半径1.0km")
    print("   H3 res 8: 边长0.5km, 外接圆半径0.5km, 内切圆半径0.4km")
    
    print("\n💡 最优搜索半径策略:")
    print("   Level 1: 边长 × 0.9 = 平衡覆盖，减少重叠")
    print("   Level 2: 边长 × 0.9 = 平衡覆盖")
    print("   Level 3: 边长 × 1.1 = 重叠覆盖，确保精细覆盖")

if __name__ == "__main__":
    compare_search_radius()
