# 参数预览功能使用指南

## 🎯 功能概述

新增的参数预览功能解决了调整参数时无法直观看到效果的问题，现在你可以：

- ✅ **实时预览**：拖动滑块时立即看到网格布局变化
- ✅ **成本估算**：实时显示预估成本和网格数量
- ✅ **参数验证**：智能检测参数合理性并给出建议
- ✅ **可视化对比**：虚线预览与实际网格对比

## 🎨 界面功能

### 1. 实时预览开关
```
🔄 实时预览  [开关]
开启后可实时查看参数变化效果
```

**功能**：
- 开启：显示虚线预览网格和统计信息
- 关闭：隐藏预览，减少计算负担

### 2. 预览统计面板
```
┌─────────────────────────────────┐
│ Level 1    Level 2 (估算)       │
│   45         180               │
│ 基础成本    预估总成本           │
│ $1.44       $7.20              │
└─────────────────────────────────┘
```

**说明**：
- **Level 1**：确定的初始网格数量
- **Level 2 (估算)**：基于前3个点的平均值估算
- **基础成本**：只执行Level 1的费用
- **预估总成本**：包含Level 2的估算费用

### 3. 参数合理性验证
```
✅ 参数配置合理
⚠️ Level 1 搜索半径过大，可能导致大量重叠 (比率: 1.67)
❌ 预估成本超出预算限制
```

**验证规则**：
- **重叠检查**：搜索半径/间距比率应在 0.7-1.5 之间
- **触发阈值**：建议设置在 15-40 之间
- **成本控制**：预估费用不超过项目预算

## 🚀 使用流程

### 1. 开启预览模式
1. 进入项目的"参数"标签页
2. 开启"实时预览"开关
3. 地图上会显示虚线预览网格

### 2. 调整参数
1. 拖动任意参数滑块
2. 实时观察地图上的网格变化
3. 查看右侧统计数据更新
4. 注意底部的参数验证提示

### 3. 应用参数
1. 满意预览效果后，点击"应用参数"
2. 参数将保存到项目配置
3. 可以继续"重新生成网格"应用到实际网格

## 🎯 最佳实践

### 参数调优建议

**Level 1 参数 (宏观扫描)**：
- **间距**: 5-10km，平衡覆盖和成本
- **半径**: 3-8km，略小于间距避免过度重叠
- **推荐比率**: 半径/间距 ≈ 0.8-1.2

**Level 2 参数 (精细扫描)**：
- **间距**: 1-2km，根据密度要求调整
- **半径**: 0.5-1.5km，确保足够覆盖
- **推荐比率**: 半径/间距 ≈ 0.7-1.0

**触发阈值**：
- **保守策略**: 25-30个地点（减少Level 2生成）
- **激进策略**: 15-20个地点（更多精细扫描）
- **平衡策略**: 20-25个地点（推荐）

### 成本控制技巧

1. **先用大间距**测试区域密度
2. **观察预览统计**，估算总成本
3. **分阶段执行**，先Level 1再决定Level 2
4. **使用模拟模式**测试参数效果

## 📊 预览 vs 实际对比

| 预览特征 | 实际执行 |
|----------|----------|
| 虚线圆圈 | 实线圆圈 |
| 快速计算 | 精确生成 |
| 估算Level 2 | 基于实际触发 |
| 无API调用 | 真实API费用 |

## 🔧 技术细节

### 地理计算精度
- 使用 **Haversine公式** 计算地球表面距离
- 考虑 **地球曲率** 修正经度距离
- 确保 **边界完全覆盖** 无遗漏

### 预览算法
```javascript
// Level 1: 精确生成所有网格点
const level1Points = generateOptimalGrid(centerLat, centerLng, scanRadius, spacing);

// Level 2: 基于前3个点估算
const sampleCount = Math.min(3, level1Points.length);
const level2PerPoint = calculateAverageLevel2Count(samplePoints);
const level2Estimated = level2PerPoint * level1Points.length;
```

### 性能优化
- **延迟渲染**：只在用户停止拖动后更新
- **采样估算**：Level 2使用部分样本推算
- **图层管理**：预览和实际网格分离

## 🎉 使用示例

### 场景1：洛杉矶便利店扫描
```
项目设置：
- 中心：(34.0522, -118.2437)
- 半径：25km

推荐参数：
- Level 1: 间距7km, 半径5km
- Level 2: 间距1.4km, 半径1km  
- 触发：20个地点

预期结果：
- Level 1: ~50个网格
- Level 2: ~200个网格 (估算)
- 基础成本: $1.60
- 总预估: $8.00
```

### 场景2：密集城区精细扫描
```
推荐参数：
- Level 1: 间距5km, 半径4km
- Level 2: 间距1km, 半径0.8km
- 触发：15个地点

特点：
- 更高的覆盖密度
- 更多的Level 2触发
- 更精确的结果
```

---

通过参数预览功能，你可以在执行前就了解参数设置的效果，避免不必要的API费用，提高扫描效率！ 