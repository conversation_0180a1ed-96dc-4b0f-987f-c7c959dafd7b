#!/usr/bin/env python3
"""
L2网格形状异常分析
分析为什么L2网格会显示为不规则形状而不是标准六边形
"""

import h3
import json
from pathlib import Path

def analyze_l2_grid_shapes():
    """分析L2网格形状问题"""
    
    print("🔍 L2网格形状异常分析")
    print("=" * 60)
    
    # 分析可能的原因
    print("\n📋 可能的原因分析:")
    print("-" * 40)
    print("1. Web界面显示问题 - 网格边界绘制错误")
    print("2. H3子网格生成问题 - 生成的子网格索引异常")
    print("3. 坐标转换问题 - 经纬度转换错误")
    print("4. 地图投影问题 - 球面到平面投影变形")
    print("5. 缩放级别问题 - 地图缩放导致形状变形")
    
    # 检查实际项目中的L2网格
    print("\n🔍 实际项目L2网格检查:")
    print("-" * 40)
    
    project_name = "test-strong-overlap"
    level7_file = Path("projects") / project_name / "grids" / "level7_grids.json"
    
    if not level7_file.exists():
        print(f"❌ 项目 {project_name} 没有Level 7网格")
        return
    
    with open(level7_file, 'r', encoding='utf-8') as f:
        level7_grids = json.load(f)
    
    print(f"项目: {project_name}")
    print(f"Level 7网格数量: {len(level7_grids)}")
    
    # 分析前几个L2网格的形状
    for i, grid in enumerate(level7_grids[:5]):
        h3_index = grid['h3_index']
        
        print(f"\n网格 {i+1}: {h3_index}")
        print(f"  中心坐标: ({grid['latitude']:.6f}, {grid['longitude']:.6f})")
        print(f"  分辨率: {h3.get_resolution(h3_index)}")
        print(f"  有效性: {'✅' if h3.is_valid_cell(h3_index) else '❌'}")
        
        # 获取边界
        try:
            boundary = h3.cell_to_boundary(h3_index)
            print(f"  边界点数: {len(boundary)}")
            
            if len(boundary) == 6:
                print(f"  ✅ 标准六边形")
                
                # 分析边界点
                lats = [lat for lat, lng in boundary]
                lngs = [lng for lat, lng in boundary]
                
                lat_range = max(lats) - min(lats)
                lng_range = max(lngs) - min(lngs)
                
                print(f"  纬度范围: {lat_range:.6f}° ({lat_range*111:.1f}m)")
                print(f"  经度范围: {lng_range:.6f}° ({lng_range*111*abs(grid['latitude']):.1f}m)")
                
                # 检查形状规律性
                center_lat, center_lng = grid['latitude'], grid['longitude']
                distances = []
                for lat, lng in boundary:
                    dist = ((lat - center_lat)**2 + (lng - center_lng)**2)**0.5
                    distances.append(dist)
                
                avg_dist = sum(distances) / len(distances)
                max_deviation = max(abs(d - avg_dist) for d in distances)
                
                print(f"  平均半径: {avg_dist:.6f}°")
                print(f"  最大偏差: {max_deviation:.6f}° ({max_deviation/avg_dist*100:.1f}%)")
                
                if max_deviation/avg_dist < 0.1:
                    print(f"  ✅ 形状规律")
                else:
                    print(f"  ⚠️  形状可能不规律")
                    
            else:
                print(f"  ❌ 非标准形状: {len(boundary)} 个边界点")
                
        except Exception as e:
            print(f"  ❌ 边界获取失败: {e}")
    
    # 检查Web界面显示逻辑
    print(f"\n🌐 Web界面显示逻辑检查:")
    print("-" * 40)
    
    # 模拟Web界面中的L2网格绘制
    test_grid = level7_grids[0]
    h3_index = test_grid['h3_index']
    
    print(f"测试网格: {h3_index}")
    
    try:
        # 模拟JavaScript中的处理
        boundary = h3.cell_to_boundary(h3_index)
        js_boundary = [[lat, lng] for lat, lng in boundary]
        
        print(f"JavaScript边界格式:")
        for i, (lat, lng) in enumerate(js_boundary):
            print(f"  [{lat:.6f}, {lng:.6f}]")
        
        # 检查是否有异常坐标
        for i, (lat, lng) in enumerate(js_boundary):
            if not (-90 <= lat <= 90) or not (-180 <= lng <= 180):
                print(f"  ❌ 异常坐标 {i+1}: ({lat}, {lng})")
        
        print(f"✅ 边界坐标正常，适合Leaflet绘制")
        
    except Exception as e:
        print(f"❌ Web界面格式转换失败: {e}")
    
    # 分析地图投影影响
    print(f"\n🗺️  地图投影影响分析:")
    print("-" * 40)
    
    # 在不同纬度测试网格形状
    test_latitudes = [0, 30, 60, 80]  # 赤道到极地
    
    for lat in test_latitudes:
        print(f"\n纬度 {lat}°:")
        
        h3_index = h3.latlng_to_cell(lat, 0, 7)  # 经度0，分辨率7
        boundary = h3.cell_to_boundary(h3_index)
        
        # 计算形状变形
        lats = [lat for lat, lng in boundary]
        lngs = [lng for lat, lng in boundary]
        
        lat_range = max(lats) - min(lats)
        lng_range = max(lngs) - min(lngs)
        
        # 考虑纬度对经度距离的影响
        lng_range_corrected = lng_range * abs(lat) if lat != 0 else lng_range
        
        aspect_ratio = lat_range / lng_range if lng_range > 0 else 0
        
        print(f"  纬度范围: {lat_range:.6f}°")
        print(f"  经度范围: {lng_range:.6f}°")
        print(f"  长宽比: {aspect_ratio:.2f}")
        
        if 0.8 <= aspect_ratio <= 1.2:
            print(f"  ✅ 形状正常")
        else:
            print(f"  ⚠️  形状可能变形 (投影影响)")
    
    # 检查缩放级别影响
    print(f"\n🔍 缩放级别影响分析:")
    print("-" * 40)
    
    # 模拟不同缩放级别下的网格大小
    test_h3 = level7_grids[0]['h3_index']
    boundary = h3.cell_to_boundary(test_h3)
    
    # 计算网格在不同缩放级别下的像素大小
    lat_range = max(lat for lat, lng in boundary) - min(lat for lat, lng in boundary)
    lng_range = max(lng for lat, lng in boundary) - min(lng for lat, lng in boundary)
    
    print(f"Level 7网格大小:")
    print(f"  纬度范围: {lat_range:.6f}° ({lat_range*111000:.1f}m)")
    print(f"  经度范围: {lng_range:.6f}° ({lng_range*111000:.1f}m)")
    
    # 在不同地图缩放级别下的像素大小
    zoom_levels = [10, 12, 14, 16, 18]
    for zoom in zoom_levels:
        # 简化的像素计算 (实际更复杂)
        pixels_per_degree = 256 * (2 ** zoom) / 360
        lat_pixels = lat_range * pixels_per_degree
        lng_pixels = lng_range * pixels_per_degree
        
        print(f"  缩放级别 {zoom}: {lat_pixels:.1f} × {lng_pixels:.1f} 像素")
        
        if lat_pixels < 5 or lng_pixels < 5:
            print(f"    ⚠️  网格太小，可能显示异常")
        elif lat_pixels > 100 or lng_pixels > 100:
            print(f"    ✅ 网格大小适中")
        else:
            print(f"    ✅ 网格大小正常")
    
    # 提供解决方案建议
    print(f"\n💡 解决方案建议:")
    print("-" * 40)
    print("1. 检查Web界面中L2网格的绘制代码")
    print("2. 确认使用正确的H3边界坐标")
    print("3. 检查地图缩放级别设置")
    print("4. 验证Leaflet多边形绘制参数")
    print("5. 考虑在高缩放级别显示网格边界")
    
    # 生成测试用的HTML代码
    print(f"\n🔧 测试用HTML代码:")
    print("-" * 40)
    
    test_grid = level7_grids[0]
    boundary = h3.cell_to_boundary(test_grid['h3_index'])
    js_boundary = [[lat, lng] for lat, lng in boundary]
    
    print("// 测试L2网格绘制")
    print(f"const testBoundary = {js_boundary};")
    print("const polygon = L.polygon(testBoundary, {")
    print("    fillColor: '#f39c12',")
    print("    fillOpacity: 0.3,")
    print("    color: '#f39c12',")
    print("    weight: 2")
    print("}).addTo(map);")
    
    print(f"\n// 网格中心点")
    print(f"const center = [{test_grid['latitude']}, {test_grid['longitude']}];")
    print("const marker = L.marker(center).addTo(map);")

if __name__ == "__main__":
    analyze_l2_grid_shapes()
