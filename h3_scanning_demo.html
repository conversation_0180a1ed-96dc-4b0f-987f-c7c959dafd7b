
<!DOCTYPE html>
<html>
<head>
    <title>H3 扫描功能演示</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        #map { height: 700px; width: 100%; margin-bottom: 20px; }
        .info-panel { background: #f0f0f0; padding: 15px; border-radius: 5px; margin-bottom: 10px; }
        .controls { margin-bottom: 20px; }
        .controls button { margin: 5px; padding: 8px 15px; cursor: pointer; border: none; border-radius: 3px; }
        .level-1 { background-color: #ff4444; color: white; }
        .level-2 { background-color: #44ff44; color: black; }
        .level-3 { background-color: #4444ff; color: white; }
        .show-all { background-color: #666; color: white; }
        .clear-all { background-color: #ccc; color: black; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px; }
        .stat-card { background: white; padding: 15px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .legend { background: white; padding: 10px; border-radius: 5px; margin-bottom: 10px; }
        .legend-item { display: flex; align-items: center; margin: 5px 0; }
        .legend-color { width: 20px; height: 20px; margin-right: 10px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>🔍 H3 扫描功能演示</h1>
    
    <div class="info-panel">
        <h3>📊 演示概览</h3>
        <p><strong>测试中心:</strong> (34.0522, -118.2437)</p>
        <p><strong>地点类型:</strong> convenience_store, restaurant, gas_station</p>
        <p><strong>Mock模式:</strong> 是</p>
        <p><strong>生成时间:</strong> 2025-07-19 11:16:43</p>
    </div>
    
    <div class="legend">
        <h4>🎨 图例</h4>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #ff4444;"></div>
            <span>Level 1 - 分辨率 5, 半径 5000m</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #44ff44;"></div>
            <span>Level 2 - 分辨率 7, 半径 1000m</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #4444ff;"></div>
            <span>Level 3 - 分辨率 8, 半径 500m</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #ff6600;"></div>
            <span>🍕 地点标记</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: rgba(255, 255, 0, 0.3); border: 2px solid #ffff00;"></div>
            <span>🔍 搜索半径</span>
        </div>
    </div>
    
    <div class="controls">
        <button class="level-1" onclick="showLevel(1)">显示 Level 1</button>
        <button class="level-2" onclick="showLevel(2)">显示 Level 2</button>
        <button class="level-3" onclick="showLevel(3)">显示 Level 3</button>
        <button class="show-all" onclick="showAll()">显示全部</button>
        <button class="clear-all" onclick="clearAll()">清除全部</button>
    </div>
    
    <div id="map"></div>
    
    <div class="stats">

        <div class="stat-card">
            <h4>📊 Level 1 统计</h4>
            <ul>
                <li>H3分辨率: 5</li>
                <li>搜索半径: 5000m</li>
                <li>扫描网格: 0 个</li>
                <li>找到地点: 0 个</li>
                <li>需要钻入: 0 个网格</li>
            </ul>
        </div>

        <div class="stat-card">
            <h4>📊 Level 2 统计</h4>
            <ul>
                <li>H3分辨率: 7</li>
                <li>搜索半径: 1000m</li>
                <li>扫描网格: 5 个</li>
                <li>找到地点: 111 个</li>
                <li>需要钻入: 4 个网格</li>
            </ul>
        </div>

        <div class="stat-card">
            <h4>📊 Level 3 统计</h4>
            <ul>
                <li>H3分辨率: 8</li>
                <li>搜索半径: 500m</li>
                <li>扫描网格: 5 个</li>
                <li>找到地点: 118 个</li>
                <li>需要钻入: 0 个网格</li>
            </ul>
        </div>

        <div class="stat-card">
            <h4>🎯 总体统计</h4>
            <ul>
                <li>总API调用: 0</li>
                <li>唯一地点: 0</li>
                <li>配置层级: 3</li>
                <li>最大重试: 3</li>
            </ul>
        </div>
    </div>
    
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        // 初始化地图
        var map = L.map('map').setView([34.0522, -118.2437], 12);
        
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);
        
        // 添加中心点标记
        L.marker([34.0522, -118.2437])
            .addTo(map)
            .bindPopup('🎯 测试中心');
        
        // 存储图层
        var layers = {
            level1: L.layerGroup(),
            level2: L.layerGroup(),
            level3: L.layerGroup()
        };
        
        // 演示数据
        var demoData = {
  "center": {
    "lat": 34.0522,
    "lng": -118.2437
  },
  "config": {
    "place_types": [
      "convenience_store",
      "restaurant",
      "gas_station"
    ],
    "layer_config": [
      {
        "h3_res": 5,
        "search_radius": 5000
      },
      {
        "h3_res": 7,
        "search_radius": 1000
      },
      {
        "h3_res": 8,
        "search_radius": 500
      }
    ],
    "mock_mode": true
  },
  "scanning_demo": [
    {
      "level": 1,
      "h3_resolution": 5,
      "search_radius": 5000,
      "grid_count": 0,
      "results": []
    },
    {
      "level": 2,
      "h3_resolution": 7,
      "search_radius": 1000,
      "grid_count": 5,
      "results": [
        {
          "grid_id": "8729a1d75ffffff",
          "center": {
            "lat": 34.04390253163188,
            "lng": -118.2443312617128
          },
          "boundary": [
            [
              34.03258192893528,
              -118.23603417801797
            ],
            [
              34.04445016530797,
              -118.22816225703191
            ],
            [
              34.05577051319852,
              -118.2364597312414
            ],
            [
              34.05522140918642,
              -118.25262970463103
            ],
            [
              34.043352821471075,
              -118.26049887907232
            ],
            [
              34.032033689042564,
              -118.25220082760907
            ]
          ],
          "places_count": 24,
          "should_drill_down": true,
          "success": true,
          "places": [
            {
              "name": "Mock Convenience Store #1",
              "id": "MOCK_convenience_store_34.0439_-118.2443_0",
              "lat": 34.04290253163188,
              "lng": -118.2453312617128,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #2",
              "id": "MOCK_convenience_store_34.0439_-118.2443_1",
              "lat": 34.04390253163188,
              "lng": -118.2443312617128,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Restaurant #1",
              "id": "MOCK_restaurant_34.0439_-118.2443_0",
              "lat": 34.04490253163188,
              "lng": -118.2433312617128,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Restaurant #2",
              "id": "MOCK_restaurant_34.0439_-118.2443_1",
              "lat": 34.04290253163188,
              "lng": -118.2453312617128,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Restaurant #3",
              "id": "MOCK_restaurant_34.0439_-118.2443_2",
              "lat": 34.04390253163188,
              "lng": -118.2443312617128,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Restaurant #4",
              "id": "MOCK_restaurant_34.0439_-118.2443_3",
              "lat": 34.04490253163188,
              "lng": -118.2433312617128,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Restaurant #5",
              "id": "MOCK_restaurant_34.0439_-118.2443_4",
              "lat": 34.04290253163188,
              "lng": -118.2453312617128,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Restaurant #6",
              "id": "MOCK_restaurant_34.0439_-118.2443_5",
              "lat": 34.04390253163188,
              "lng": -118.2443312617128,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Restaurant #7",
              "id": "MOCK_restaurant_34.0439_-118.2443_6",
              "lat": 34.04490253163188,
              "lng": -118.2433312617128,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Restaurant #8",
              "id": "MOCK_restaurant_34.0439_-118.2443_7",
              "lat": 34.04290253163188,
              "lng": -118.2453312617128,
              "types": [
                "restaurant"
              ]
            }
          ],
          "search_radius": 1000,
          "metadata": {
            "api_calls": 3,
            "scan_time": "2025-07-19T11:16:43.331381",
            "grid_center": [
              34.04390253163188,
              -118.2443312617128
            ],
            "search_radius": 1000,
            "h3_resolution": 7,
            "level": 2,
            "place_types": [
              "convenience_store",
              "restaurant",
              "gas_station"
            ],
            "execution_time_seconds": 0.03541707992553711
          }
        },
        {
          "grid_id": "8729a1d74ffffff",
          "center": {
            "lat": 34.03312809279967,
            "lng": -118.21986614262346
          },
          "boundary": [
            [
              34.0218042969173,
              -118.21157138802961
            ],
            [
              34.03367218004759,
              -118.20369672382253
            ],
            [
              34.044995721910404,
              -118.21199186742669
            ],
            [
              34.04445016530797,
              -118.22816225703191
            ],
            [
              34.03258192893528,
              -118.23603417801797
            ],
            [
              34.021259602339754,
              -118.22773845356052
            ]
          ],
          "places_count": 24,
          "should_drill_down": true,
          "success": true,
          "places": [
            {
              "name": "Mock Convenience Store #1",
              "id": "MOCK_convenience_store_34.0331_-118.2199_0",
              "lat": 34.032128092799674,
              "lng": -118.22086614262346,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #2",
              "id": "MOCK_convenience_store_34.0331_-118.2199_1",
              "lat": 34.03312809279967,
              "lng": -118.21986614262346,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #3",
              "id": "MOCK_convenience_store_34.0331_-118.2199_2",
              "lat": 34.03412809279967,
              "lng": -118.21886614262345,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #4",
              "id": "MOCK_convenience_store_34.0331_-118.2199_3",
              "lat": 34.032128092799674,
              "lng": -118.22086614262346,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #5",
              "id": "MOCK_convenience_store_34.0331_-118.2199_4",
              "lat": 34.03312809279967,
              "lng": -118.21986614262346,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #6",
              "id": "MOCK_convenience_store_34.0331_-118.2199_5",
              "lat": 34.03412809279967,
              "lng": -118.21886614262345,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #7",
              "id": "MOCK_convenience_store_34.0331_-118.2199_6",
              "lat": 34.032128092799674,
              "lng": -118.22086614262346,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #8",
              "id": "MOCK_convenience_store_34.0331_-118.2199_7",
              "lat": 34.03312809279967,
              "lng": -118.21986614262346,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Restaurant #1",
              "id": "MOCK_restaurant_34.0331_-118.2199_0",
              "lat": 34.03412809279967,
              "lng": -118.21886614262345,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Restaurant #2",
              "id": "MOCK_restaurant_34.0331_-118.2199_1",
              "lat": 34.032128092799674,
              "lng": -118.22086614262346,
              "types": [
                "restaurant"
              ]
            }
          ],
          "search_radius": 1000,
          "metadata": {
            "api_calls": 3,
            "scan_time": "2025-07-19T11:16:43.367095",
            "grid_center": [
              34.03312809279967,
              -118.21986614262346
            ],
            "search_radius": 1000,
            "h3_resolution": 7,
            "level": 2,
            "place_types": [
              "convenience_store",
              "restaurant",
              "gas_station"
            ],
            "execution_time_seconds": 0.03483891487121582
          }
        },
        {
          "grid_id": "8729a1d70ffffff",
          "center": {
            "lat": 34.056317539680386,
            "lng": -118.22028837140907
          },
          "boundary": [
            [
              34.044995721910404,
              -118.21199186742669
            ],
            [
              34.05686248804333,
              -118.20411562753252
            ],
            [
              34.06818405042416,
              -118.21241252071142
            ],
            [
              34.067637632114135,
              -118.22858623575635
            ],
            [
              34.05577051319852,
              -118.2364597312414
            ],
            [
              34.04445016530797,
              -118.22816225703191
            ]
          ],
          "places_count": 28,
          "should_drill_down": true,
          "success": true,
          "places": [
            {
              "name": "Mock Convenience Store #1",
              "id": "MOCK_convenience_store_34.0563_-118.2203_0",
              "lat": 34.05531753968039,
              "lng": -118.22128837140907,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #2",
              "id": "MOCK_convenience_store_34.0563_-118.2203_1",
              "lat": 34.056317539680386,
              "lng": -118.22028837140907,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Restaurant #1",
              "id": "MOCK_restaurant_34.0563_-118.2203_0",
              "lat": 34.05731753968038,
              "lng": -118.21928837140906,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Restaurant #2",
              "id": "MOCK_restaurant_34.0563_-118.2203_1",
              "lat": 34.05531753968039,
              "lng": -118.22128837140907,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Restaurant #3",
              "id": "MOCK_restaurant_34.0563_-118.2203_2",
              "lat": 34.056317539680386,
              "lng": -118.22028837140907,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Restaurant #4",
              "id": "MOCK_restaurant_34.0563_-118.2203_3",
              "lat": 34.05731753968038,
              "lng": -118.21928837140906,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Restaurant #5",
              "id": "MOCK_restaurant_34.0563_-118.2203_4",
              "lat": 34.05531753968039,
              "lng": -118.22128837140907,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Restaurant #6",
              "id": "MOCK_restaurant_34.0563_-118.2203_5",
              "lat": 34.056317539680386,
              "lng": -118.22028837140907,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Restaurant #7",
              "id": "MOCK_restaurant_34.0563_-118.2203_6",
              "lat": 34.05731753968038,
              "lng": -118.21928837140906,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Restaurant #8",
              "id": "MOCK_restaurant_34.0563_-118.2203_7",
              "lat": 34.05531753968039,
              "lng": -118.22128837140907,
              "types": [
                "restaurant"
              ]
            }
          ],
          "search_radius": 1000,
          "metadata": {
            "api_calls": 3,
            "scan_time": "2025-07-19T11:16:43.402011",
            "grid_center": [
              34.056317539680386,
              -118.22028837140907
            ],
            "search_radius": 1000,
            "h3_resolution": 7,
            "level": 2,
            "place_types": [
              "convenience_store",
              "restaurant",
              "gas_station"
            ],
            "execution_time_seconds": 0.035388946533203125
          }
        },
        {
          "grid_id": "8729a1d71ffffff",
          "center": {
            "lat": 34.06708913522847,
            "lng": -118.24475856523819
          },
          "boundary": [
            [
              34.05577051319852,
              -118.2364597312414
            ],
            [
              34.067637632114135,
              -118.22858623575635
            ],
            [
              34.07895599796916,
              -118.23688546045496
            ],
            [
              34.07840603015583,
              -118.25305875900828
            ],
            [
              34.06653856035659,
              -118.26092950675842
            ],
            [
              34.05522140918642,
              -118.25262970463103
            ]
          ],
          "places_count": 9,
          "should_drill_down": false,
          "success": true,
          "places": [
            {
              "name": "Mock Convenience Store #1",
              "id": "MOCK_convenience_store_34.0671_-118.2448_0",
              "lat": 34.066089135228474,
              "lng": -118.24575856523819,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #2",
              "id": "MOCK_convenience_store_34.0671_-118.2448_1",
              "lat": 34.06708913522847,
              "lng": -118.24475856523819,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #3",
              "id": "MOCK_convenience_store_34.0671_-118.2448_2",
              "lat": 34.06808913522847,
              "lng": -118.24375856523818,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Restaurant #1",
              "id": "MOCK_restaurant_34.0671_-118.2448_0",
              "lat": 34.066089135228474,
              "lng": -118.24575856523819,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Restaurant #2",
              "id": "MOCK_restaurant_34.0671_-118.2448_1",
              "lat": 34.06708913522847,
              "lng": -118.24475856523819,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Gas Station #1",
              "id": "MOCK_gas_station_34.0671_-118.2448_0",
              "lat": 34.06808913522847,
              "lng": -118.24375856523818,
              "types": [
                "gas_station"
              ]
            },
            {
              "name": "Mock Gas Station #2",
              "id": "MOCK_gas_station_34.0671_-118.2448_1",
              "lat": 34.066089135228474,
              "lng": -118.24575856523819,
              "types": [
                "gas_station"
              ]
            },
            {
              "name": "Mock Gas Station #3",
              "id": "MOCK_gas_station_34.0671_-118.2448_2",
              "lat": 34.06708913522847,
              "lng": -118.24475856523819,
              "types": [
                "gas_station"
              ]
            },
            {
              "name": "Mock Gas Station #4",
              "id": "MOCK_gas_station_34.0671_-118.2448_3",
              "lat": 34.06808913522847,
              "lng": -118.24375856523818,
              "types": [
                "gas_station"
              ]
            }
          ],
          "search_radius": 1000,
          "metadata": {
            "api_calls": 3,
            "scan_time": "2025-07-19T11:16:43.437454",
            "grid_center": [
              34.06708913522847,
              -118.24475856523819
            ],
            "search_radius": 1000,
            "h3_resolution": 7,
            "level": 2,
            "place_types": [
              "convenience_store",
              "restaurant",
              "gas_station"
            ],
            "execution_time_seconds": 0.03744697570800781
          }
        },
        {
          "grid_id": "8729a1d62ffffff",
          "center": {
            "lat": 34.05467022823382,
            "lng": -118.2687982891808
          },
          "boundary": [
            [
              34.043352821471075,
              -118.26049887907232
            ],
            [
              34.05522140918642,
              -118.25262970463103
            ],
            [
              34.06653856035659,
              -118.26092950675842
            ],
            [
              34.06598590808902,
              -118.27709905791978
            ],
            [
              34.05411697093147,
              -118.28496548249475
            ],
            [
              34.04280103541551,
              -118.27666510671465
            ]
          ],
          "places_count": 26,
          "should_drill_down": true,
          "success": true,
          "places": [
            {
              "name": "Mock Convenience Store #1",
              "id": "MOCK_convenience_store_34.0547_-118.2688_0",
              "lat": 34.05367022823382,
              "lng": -118.2697982891808,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #2",
              "id": "MOCK_convenience_store_34.0547_-118.2688_1",
              "lat": 34.05467022823382,
              "lng": -118.2687982891808,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #3",
              "id": "MOCK_convenience_store_34.0547_-118.2688_2",
              "lat": 34.055670228233815,
              "lng": -118.26779828918079,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #4",
              "id": "MOCK_convenience_store_34.0547_-118.2688_3",
              "lat": 34.05367022823382,
              "lng": -118.2697982891808,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #5",
              "id": "MOCK_convenience_store_34.0547_-118.2688_4",
              "lat": 34.05467022823382,
              "lng": -118.2687982891808,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #6",
              "id": "MOCK_convenience_store_34.0547_-118.2688_5",
              "lat": 34.055670228233815,
              "lng": -118.26779828918079,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #7",
              "id": "MOCK_convenience_store_34.0547_-118.2688_6",
              "lat": 34.05367022823382,
              "lng": -118.2697982891808,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #8",
              "id": "MOCK_convenience_store_34.0547_-118.2688_7",
              "lat": 34.05467022823382,
              "lng": -118.2687982891808,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #9",
              "id": "MOCK_convenience_store_34.0547_-118.2688_8",
              "lat": 34.055670228233815,
              "lng": -118.26779828918079,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #10",
              "id": "MOCK_convenience_store_34.0547_-118.2688_9",
              "lat": 34.05367022823382,
              "lng": -118.2697982891808,
              "types": [
                "convenience_store"
              ]
            }
          ],
          "search_radius": 1000,
          "metadata": {
            "api_calls": 3,
            "scan_time": "2025-07-19T11:16:43.474982",
            "grid_center": [
              34.05467022823382,
              -118.2687982891808
            ],
            "search_radius": 1000,
            "h3_resolution": 7,
            "level": 2,
            "place_types": [
              "convenience_store",
              "restaurant",
              "gas_station"
            ],
            "execution_time_seconds": 0.03766202926635742
          }
        }
      ]
    },
    {
      "level": 3,
      "h3_resolution": 8,
      "search_radius": 500,
      "grid_count": 5,
      "results": [
        {
          "grid_id": "8829a1d757fffff",
          "center": {
            "lat": 34.052301408255126,
            "lng": -118.24101892542859
          },
          "boundary": [
            [
              34.04737180721559,
              -118.23977258442022
            ],
            [
              34.05084092055802,
              -118.23521352616076
            ],
            [
              34.05577051319852,
              -118.2364597312414
            ],
            [
              34.05723082998393,
              -118.2422653692267
            ],
            [
              34.053761571136256,
              -118.24682431829173
            ],
            [
              34.04883214102074,
              -118.24557773860613
            ]
          ],
          "places_count": 9,
          "should_drill_down": false,
          "success": true,
          "places": [
            {
              "name": "Mock Convenience Store #1",
              "id": "MOCK_convenience_store_34.0523_-118.2410_0",
              "lat": 34.05130140825513,
              "lng": -118.2420189254286,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #2",
              "id": "MOCK_convenience_store_34.0523_-118.2410_1",
              "lat": 34.052301408255126,
              "lng": -118.24101892542859,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #3",
              "id": "MOCK_convenience_store_34.0523_-118.2410_2",
              "lat": 34.05330140825512,
              "lng": -118.24001892542859,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #4",
              "id": "MOCK_convenience_store_34.0523_-118.2410_3",
              "lat": 34.05130140825513,
              "lng": -118.2420189254286,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Restaurant #1",
              "id": "MOCK_restaurant_34.0523_-118.2410_0",
              "lat": 34.052301408255126,
              "lng": -118.24101892542859,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Restaurant #2",
              "id": "MOCK_restaurant_34.0523_-118.2410_1",
              "lat": 34.05330140825512,
              "lng": -118.24001892542859,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Gas Station #1",
              "id": "MOCK_gas_station_34.0523_-118.2410_0",
              "lat": 34.05130140825513,
              "lng": -118.2420189254286,
              "types": [
                "gas_station"
              ]
            },
            {
              "name": "Mock Gas Station #2",
              "id": "MOCK_gas_station_34.0523_-118.2410_1",
              "lat": 34.052301408255126,
              "lng": -118.24101892542859,
              "types": [
                "gas_station"
              ]
            },
            {
              "name": "Mock Gas Station #3",
              "id": "MOCK_gas_station_34.0523_-118.2410_2",
              "lat": 34.05330140825512,
              "lng": -118.24001892542859,
              "types": [
                "gas_station"
              ]
            }
          ],
          "search_radius": 500,
          "metadata": {
            "api_calls": 3,
            "scan_time": "2025-07-19T11:16:43.512934",
            "grid_center": [
              34.052301408255126,
              -118.24101892542859
            ],
            "search_radius": 500,
            "h3_resolution": 8,
            "level": 3,
            "place_types": [
              "convenience_store",
              "restaurant",
              "gas_station"
            ],
            "execution_time_seconds": 0.03734302520751953
          }
        },
        {
          "grid_id": "8829a1d755fffff",
          "center": {
            "lat": 34.045911148639675,
            "lng": -118.23396742385574
          },
          "boundary": [
            [
              34.04098119751372,
              -118.23272142431475
            ],
            [
              34.04445016530797,
              -118.22816225703191
            ],
            [
              34.04938010805995,
              -118.22940812060747
            ],
            [
              34.05084092055802,
              -118.23521352616076
            ],
            [
              34.04737180721559,
              -118.23977258442022
            ],
            [
              34.0424420269356,
              -118.23852634618999
            ]
          ],
          "places_count": 39,
          "should_drill_down": false,
          "success": true,
          "places": [
            {
              "name": "Mock Convenience Store #1",
              "id": "MOCK_convenience_store_34.0459_-118.2340_0",
              "lat": 34.04491114863968,
              "lng": -118.23496742385575,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #2",
              "id": "MOCK_convenience_store_34.0459_-118.2340_1",
              "lat": 34.045911148639675,
              "lng": -118.23396742385574,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #3",
              "id": "MOCK_convenience_store_34.0459_-118.2340_2",
              "lat": 34.04691114863967,
              "lng": -118.23296742385574,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #4",
              "id": "MOCK_convenience_store_34.0459_-118.2340_3",
              "lat": 34.04491114863968,
              "lng": -118.23496742385575,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #5",
              "id": "MOCK_convenience_store_34.0459_-118.2340_4",
              "lat": 34.045911148639675,
              "lng": -118.23396742385574,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #6",
              "id": "MOCK_convenience_store_34.0459_-118.2340_5",
              "lat": 34.04691114863967,
              "lng": -118.23296742385574,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #7",
              "id": "MOCK_convenience_store_34.0459_-118.2340_6",
              "lat": 34.04491114863968,
              "lng": -118.23496742385575,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #8",
              "id": "MOCK_convenience_store_34.0459_-118.2340_7",
              "lat": 34.045911148639675,
              "lng": -118.23396742385574,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #9",
              "id": "MOCK_convenience_store_34.0459_-118.2340_8",
              "lat": 34.04691114863967,
              "lng": -118.23296742385574,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #10",
              "id": "MOCK_convenience_store_34.0459_-118.2340_9",
              "lat": 34.04491114863968,
              "lng": -118.23496742385575,
              "types": [
                "convenience_store"
              ]
            }
          ],
          "search_radius": 500,
          "metadata": {
            "api_calls": 3,
            "scan_time": "2025-07-19T11:16:43.550347",
            "grid_center": [
              34.045911148639675,
              -118.23396742385574
            ],
            "search_radius": 500,
            "h3_resolution": 8,
            "level": 3,
            "place_types": [
              "convenience_store",
              "restaurant",
              "gas_station"
            ],
            "execution_time_seconds": 0.037596940994262695
          }
        },
        {
          "grid_id": "8829a1d70bfffff",
          "center": {
            "lat": 34.05430987156694,
            "lng": -118.23065408694453
          },
          "boundary": [
            [
              34.04938010805995,
              -118.22940812060747
            ],
            [
              34.05284890510425,
              -118.22484843645526
            ],
            [
              34.057778660150205,
              -118.22609426678142
            ],
            [
              34.059239455758735,
              -118.23190015605451
            ],
            [
              34.05577051319852,
              -118.2364597312414
            ],
            [
              34.05084092055802,
              -118.23521352616076
            ]
          ],
          "places_count": 43,
          "should_drill_down": false,
          "success": true,
          "places": [
            {
              "name": "Mock Convenience Store #1",
              "id": "MOCK_convenience_store_34.0543_-118.2307_0",
              "lat": 34.053309871566945,
              "lng": -118.23165408694453,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #2",
              "id": "MOCK_convenience_store_34.0543_-118.2307_1",
              "lat": 34.05430987156694,
              "lng": -118.23065408694453,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #3",
              "id": "MOCK_convenience_store_34.0543_-118.2307_2",
              "lat": 34.05530987156694,
              "lng": -118.22965408694452,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #4",
              "id": "MOCK_convenience_store_34.0543_-118.2307_3",
              "lat": 34.053309871566945,
              "lng": -118.23165408694453,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #5",
              "id": "MOCK_convenience_store_34.0543_-118.2307_4",
              "lat": 34.05430987156694,
              "lng": -118.23065408694453,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #6",
              "id": "MOCK_convenience_store_34.0543_-118.2307_5",
              "lat": 34.05530987156694,
              "lng": -118.22965408694452,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #7",
              "id": "MOCK_convenience_store_34.0543_-118.2307_6",
              "lat": 34.053309871566945,
              "lng": -118.23165408694453,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #8",
              "id": "MOCK_convenience_store_34.0543_-118.2307_7",
              "lat": 34.05430987156694,
              "lng": -118.23065408694453,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #9",
              "id": "MOCK_convenience_store_34.0543_-118.2307_8",
              "lat": 34.05530987156694,
              "lng": -118.22965408694452,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #10",
              "id": "MOCK_convenience_store_34.0543_-118.2307_9",
              "lat": 34.053309871566945,
              "lng": -118.23165408694453,
              "types": [
                "convenience_store"
              ]
            }
          ],
          "search_radius": 500,
          "metadata": {
            "api_calls": 3,
            "scan_time": "2025-07-19T11:16:43.588017",
            "grid_center": [
              34.05430987156694,
              -118.23065408694453
            ],
            "search_radius": 500,
            "h3_resolution": 8,
            "level": 3,
            "place_types": [
              "convenience_store",
              "restaurant",
              "gas_station"
            ],
            "execution_time_seconds": 0.036776065826416016
          }
        },
        {
          "grid_id": "8829a1d71dfffff",
          "center": {
            "lat": 34.06069992649094,
            "lng": -118.23770603910913
          },
          "boundary": [
            [
              34.05577051319852,
              -118.2364597312414
            ],
            [
              34.059239455758735,
              -118.23190015605451
            ],
            [
              34.06416886056507,
              -118.23314632794903
            ],
            [
              34.06562916036502,
              -118.23895244977552
            ],
            [
              34.06216007233179,
              -118.24351191582605
            ],
            [
              34.05723082998393,
              -118.2422653692267
            ]
          ],
          "places_count": 21,
          "should_drill_down": false,
          "success": true,
          "places": [
            {
              "name": "Mock Convenience Store #1",
              "id": "MOCK_convenience_store_34.0607_-118.2377_0",
              "lat": 34.05969992649094,
              "lng": -118.23870603910913,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Convenience Store #2",
              "id": "MOCK_convenience_store_34.0607_-118.2377_1",
              "lat": 34.06069992649094,
              "lng": -118.23770603910913,
              "types": [
                "convenience_store"
              ]
            },
            {
              "name": "Mock Restaurant #1",
              "id": "MOCK_restaurant_34.0607_-118.2377_0",
              "lat": 34.06169992649094,
              "lng": -118.23670603910912,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Restaurant #2",
              "id": "MOCK_restaurant_34.0607_-118.2377_1",
              "lat": 34.05969992649094,
              "lng": -118.23870603910913,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Gas Station #1",
              "id": "MOCK_gas_station_34.0607_-118.2377_0",
              "lat": 34.06069992649094,
              "lng": -118.23770603910913,
              "types": [
                "gas_station"
              ]
            },
            {
              "name": "Mock Gas Station #2",
              "id": "MOCK_gas_station_34.0607_-118.2377_1",
              "lat": 34.06169992649094,
              "lng": -118.23670603910912,
              "types": [
                "gas_station"
              ]
            },
            {
              "name": "Mock Gas Station #3",
              "id": "MOCK_gas_station_34.0607_-118.2377_2",
              "lat": 34.05969992649094,
              "lng": -118.23870603910913,
              "types": [
                "gas_station"
              ]
            },
            {
              "name": "Mock Gas Station #4",
              "id": "MOCK_gas_station_34.0607_-118.2377_3",
              "lat": 34.06069992649094,
              "lng": -118.23770603910913,
              "types": [
                "gas_station"
              ]
            },
            {
              "name": "Mock Gas Station #5",
              "id": "MOCK_gas_station_34.0607_-118.2377_4",
              "lat": 34.06169992649094,
              "lng": -118.23670603910912,
              "types": [
                "gas_station"
              ]
            },
            {
              "name": "Mock Gas Station #6",
              "id": "MOCK_gas_station_34.0607_-118.2377_5",
              "lat": 34.05969992649094,
              "lng": -118.23870603910913,
              "types": [
                "gas_station"
              ]
            }
          ],
          "search_radius": 500,
          "metadata": {
            "api_calls": 3,
            "scan_time": "2025-07-19T11:16:43.624888",
            "grid_center": [
              34.06069992649094,
              -118.23770603910913
            ],
            "search_radius": 500,
            "h3_resolution": 8,
            "level": 3,
            "place_types": [
              "convenience_store",
              "restaurant",
              "gas_station"
            ],
            "execution_time_seconds": 0.036760807037353516
          }
        },
        {
          "grid_id": "8829a1d719fffff",
          "center": {
            "lat": 34.05869082190819,
            "lng": -118.2480710007812
          },
          "boundary": [
            [
              34.053761571136256,
              -118.24682431829173
            ],
            [
              34.05723082998393,
              -118.2422653692267
            ],
            [
              34.06216007233179,
              -118.24351191582605
            ],
            [
              34.06361989326632,
              -118.24931778608607
            ],
            [
              34.06015048895632,
              -118.25387662578572
            ],
            [
              34.05522140918642,
              -118.25262970463103
            ]
          ],
          "places_count": 6,
          "should_drill_down": false,
          "success": true,
          "places": [
            {
              "name": "Mock Restaurant #1",
              "id": "MOCK_restaurant_34.0587_-118.2481_0",
              "lat": 34.057690821908196,
              "lng": -118.2490710007812,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Restaurant #2",
              "id": "MOCK_restaurant_34.0587_-118.2481_1",
              "lat": 34.05869082190819,
              "lng": -118.2480710007812,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Restaurant #3",
              "id": "MOCK_restaurant_34.0587_-118.2481_2",
              "lat": 34.05969082190819,
              "lng": -118.24707100078119,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Restaurant #4",
              "id": "MOCK_restaurant_34.0587_-118.2481_3",
              "lat": 34.057690821908196,
              "lng": -118.2490710007812,
              "types": [
                "restaurant"
              ]
            },
            {
              "name": "Mock Gas Station #1",
              "id": "MOCK_gas_station_34.0587_-118.2481_0",
              "lat": 34.05869082190819,
              "lng": -118.2480710007812,
              "types": [
                "gas_station"
              ]
            },
            {
              "name": "Mock Gas Station #2",
              "id": "MOCK_gas_station_34.0587_-118.2481_1",
              "lat": 34.05969082190819,
              "lng": -118.24707100078119,
              "types": [
                "gas_station"
              ]
            }
          ],
          "search_radius": 500,
          "metadata": {
            "api_calls": 3,
            "scan_time": "2025-07-19T11:16:43.661725",
            "grid_center": [
              34.05869082190819,
              -118.2480710007812
            ],
            "search_radius": 500,
            "h3_resolution": 8,
            "level": 3,
            "place_types": [
              "convenience_store",
              "restaurant",
              "gas_station"
            ],
            "execution_time_seconds": 0.03464221954345703
          }
        }
      ]
    }
  ],
  "final_stats": {
    "api_calls_made": 0,
    "unique_places_found": 0,
    "mock_mode": true,
    "config": {
      "place_types": [
        "convenience_store",
        "restaurant",
        "gas_station"
      ],
      "layer_count": 3,
      "max_retries": 3,
      "timeout_seconds": 30
    }
  }
};
        
        // 颜色配置
        var levelColors = {
            1: '#ff4444',
            2: '#44ff44',
            3: '#4444ff'
        };
        
        // 创建图层
        function createLayers() {
            demoData.scanning_demo.forEach(function(levelData) {
                var level = levelData.level;
                var color = levelColors[level];
                var layerGroup = layers['level' + level];
                
                levelData.results.forEach(function(result) {
                    // 创建网格多边形
                    var polygon = L.polygon(result.boundary, {
                        color: color,
                        weight: 2,
                        fillOpacity: 0.2
                    });
                    
                    // 创建搜索半径圆圈
                    var searchCircle = L.circle([result.center.lat, result.center.lng], {
                        color: '#ffff00',
                        fillColor: '#ffff00',
                        fillOpacity: 0.1,
                        weight: 2,
                        radius: result.search_radius
                    });
                    
                    // 创建弹出窗口内容
                    var popupContent = '<b>Level ' + level + ' 扫描结果</b><br>' +
                        'Grid ID: ' + result.grid_id + '<br>' +
                        '搜索半径: ' + result.search_radius + 'm<br>' +
                        '找到地点: ' + result.places_count + ' 个<br>' +
                        '需要钻入: ' + (result.should_drill_down ? '是' : '否') + '<br>' +
                        '成功: ' + (result.success ? '是' : '否');
                    
                    if (result.places.length > 0) {
                        popupContent += '<br><br><b>地点列表:</b>';
                        result.places.forEach(function(place, index) {
                            popupContent += '<br>' + (index + 1) + '. ' + place.name;
                        });
                    }
                    
                    polygon.bindPopup(popupContent);
                    searchCircle.bindPopup(popupContent);
                    
                    layerGroup.addLayer(polygon);
                    layerGroup.addLayer(searchCircle);
                    
                    // 添加地点标记
                    result.places.forEach(function(place) {
                        var marker = L.circleMarker([place.lat, place.lng], {
                            color: '#ff6600',
                            fillColor: '#ff6600',
                            fillOpacity: 0.8,
                            radius: 5
                        }).bindPopup(
                            '<b>' + place.name + '</b><br>' +
                            'ID: ' + place.id + '<br>' +
                            '位置: (' + place.lat.toFixed(4) + ', ' + place.lng.toFixed(4) + ')'
                        );
                        
                        layerGroup.addLayer(marker);
                    });
                });
            });
        }
        
        // 控制函数
        function showLevel(level) {
            clearAll();
            layers['level' + level].addTo(map);
        }
        
        function showAll() {
            Object.values(layers).forEach(layer => layer.addTo(map));
        }
        
        function clearAll() {
            Object.values(layers).forEach(layer => map.removeLayer(layer));
        }
        
        // 初始化
        createLayers();
        showLevel(1); // 默认显示Level 1
    </script>
</body>
</html>
