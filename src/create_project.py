#!/usr/bin/env python3
"""
项目创建和管理命令行工具

支持的操作：
- 创建新项目
- 列出现有项目
- 生成项目网格
- 查看项目状态
"""

import argparse
import sys
from typing import Optional

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.project_manager import ProjectManager, ProjectConfig


def create_project(args):
    """创建新项目"""
    try:
        manager = ProjectManager(args.projects_dir)
        project_dir = manager.create_project(
            name=args.name,
            center_lat=args.center_lat,
            center_lng=args.center_lng,
            scan_radius=args.radius,
            description=args.description or f"扫描区域: {args.name}"
        )
        
        print(f"🎉 项目创建成功！")
        print(f"📁 项目目录: {project_dir}")
        print(f"📍 中心坐标: ({args.center_lat}, {args.center_lng})")
        print(f"📏 扫描半径: {args.radius} km")
        print()
        print("下一步操作:")
        print(f"  1. 调整参数: python src/tune_parameters.py --project {args.name}")
        print(f"  2. 生成Level1网格: python src/create_project.py --generate-grids {args.name}")
        print(f"  3. 启动Web界面: python src/project_api.py")
        print(f"  💡 新特性: 支持真实API和模拟模式切换，Level2/3网格动态生成")
        
    except ValueError as e:
        print(f"❌ 错误: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 创建项目失败: {e}")
        sys.exit(1)


def list_projects(args):
    """列出所有项目"""
    try:
        manager = ProjectManager(args.projects_dir)
        projects = manager.list_projects()
        
        if not projects:
            print("📂 暂无项目")
            print()
            print("创建第一个项目:")
            print("  python src/create_project.py --create 项目名 --center 34.0522,-118.2437 --radius 25")
            return
        
        print(f"📂 发现 {len(projects)} 个项目:\n")
        
        for project in projects:
            _, config = manager.load_project(project['name'])
            print(f"🗂️  {project['name']}")
            print(f"   📍 中心: ({project['center'][0]:.4f}, {project['center'][1]:.4f})")
            print(f"   📏 半径: {project['radius']} km")
            print(f"   📝 说明: {project['description']}")
            
            stats = project['stats']
            if 'error' not in stats:
                l1_key = f"level{config.h3_res_level1}"
                l2_key = f"level{config.h3_res_level2}"
                l3_key = f"level{config.h3_res_level3}"
                l1_count = stats['by_level'].get(l1_key, 0)
                l2_count = stats['by_level'].get(l2_key, 0)
                l3_count = stats['by_level'].get(l3_key, 0)
                print(f"   📊 网格: {stats['total_grids']} 个 (L{config.h3_res_level1}:{l1_count}, L{config.h3_res_level2}:{l2_count}, L{config.h3_res_level3}:{l3_count})")
                print(f"   🎯 状态: ✅{stats['by_status'].get('completed',0)} ⏳{stats['by_status'].get('pending',0)} ❌{stats['by_status'].get('failed',0)}")
                print(f"   💰 成本: ${stats['total_cost']:.2f}")
                print(f"   📍 地点: {stats['total_places']} 个")
            
            print(f"   🕐 创建: {project['created_at'][:19].replace('T', ' ')}")
            if project['updated_at'] != project['created_at']:
                print(f"   🔄 更新: {project['updated_at'][:19].replace('T', ' ')}")
            print()
            
    except Exception as e:
        print(f"❌ 列出项目失败: {e}")
        sys.exit(1)


def generate_grids(args):
    """为项目生成网格"""
    try:
        manager = ProjectManager(args.projects_dir)
        
        # 验证项目存在
        project_dir, config = manager.load_project(args.project)
        
        print(f"🔧 为项目 '{args.project}' 生成网格...")
        print(f"📍 中心坐标: ({config.center_latitude}, {config.center_longitude})")
        print(f"📏 扫描半径: {config.scan_radius_km} km")
        print(f"⚙️  H3网格参数:")
        print(f"   Level {config.h3_res_level1}: 搜索半径 {config.search_radius_level1/1000}km")
        print(f"   Level {config.h3_res_level2}: 搜索半径 {config.search_radius_level2/1000}km")
        print(f"   Level {config.h3_res_level3}: 搜索半径 {config.search_radius_level3/1000}km")
        print(f"   触发阈值: {config.recursion_trigger_count} 个地点")
        print()
        
        stats = manager.generate_grids(args.project)

        l1_key = f"level{config.h3_res_level1}"
        l2_key = f"level{config.h3_res_level2}"
        l3_key = f"level{config.h3_res_level3}"
        
        print(f"✅ 初始网格生成完成!")
        print(f"📊 Level {config.h3_res_level1}: {stats[l1_key]} 个网格 (初始生成)")
        print(f"📊 Level {config.h3_res_level2}: {stats[l2_key]} 个网格 (动态生成)")
        print(f"📊 Level {config.h3_res_level3}: {stats[l3_key]} 个网格 (动态生成)")
        print(f"📊 当前总计: {sum(stats.values())} 个网格")
        
        base_cost = stats[l1_key] * config.api_cost_per_call
        print(f"💰 Level 1基础成本: ${base_cost:.2f}")
        print(f"📋 Level 2/3网格将在执行过程中根据触发阈值动态生成")
        
        print()
        print("下一步操作:")
        print(f"  可视化项目: python src/run_project.py --project {args.project}")
        print(f"  启动Web界面: python src/project_api.py")
        print(f"  💡 在Web界面中可以选择真实API或模拟模式执行")
        
    except ValueError as e:
        print(f"❌ 错误: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 生成网格失败: {e}")
        sys.exit(1)


def show_project_status(args):
    """显示项目详细状态"""
    try:
        manager = ProjectManager(args.projects_dir)
        project_dir, config = manager.load_project(args.project)
        stats = manager.get_project_stats(args.project)
        
        print(f"📋 项目状态: {config.name}")
        print("=" * 50)
        print(f"📁 目录: {project_dir}")
        print(f"📝 说明: {config.description}")
        print(f"📍 中心: ({config.center_latitude}, {config.center_longitude})")
        print(f"📏 半径: {config.scan_radius_km} km")
        print(f"🕐 创建: {config.created_at[:19].replace('T', ' ')}")
        print(f"🔄 更新: {config.updated_at[:19].replace('T', ' ')}")
        print()
        
        if 'error' in stats:
            print(f"❌ 状态错误: {stats['error']}")
            return
        
        print("📊 网格统计:")
        print(f"   总数: {stats['total_grids']} 个")
        l1_key = f"level{config.h3_res_level1}"
        l2_key = f"level{config.h3_res_level2}"
        l3_key = f"level{config.h3_res_level3}"
        print(f"   Level {config.h3_res_level1}: {stats['by_level'].get(l1_key, 0)} 个")
        print(f"   Level {config.h3_res_level2}: {stats['by_level'].get(l2_key, 0)} 个") 
        print(f"   Level {config.h3_res_level3}: {stats['by_level'].get(l3_key, 0)} 个")
        print()
        
        print("🎯 执行状态:")
        for status, count in stats['by_status'].items():
            if count > 0:
                emoji = {'pending': '⏳', 'running': '🔄', 'completed': '✅', 'failed': '❌'}.get(status, '❓')
                print(f"   {emoji} {status}: {count} 个")
        print()
        
        print("💰 成本统计:")
        print(f"   已花费: ${stats['total_cost']:.2f}")
        print(f"   发现地点: {stats['total_places']} 个")
        
        if stats['last_execution']:
            print(f"🕐 最后执行: {stats['last_execution'][:19].replace('T', ' ')}")
        
        # 显示可执行的网格
        pending_grids = manager.get_grids_by_status(args.project, 'pending')
        if pending_grids:
            print(f"\n⏳ 待执行网格: {len(pending_grids)} 个")
            print("   执行命令:")
            print(f"     python src/run_project.py --project {args.project} --all")
            print(f"     python src/run_project.py --project {args.project} --grid-ids grid_1_1,grid_1_2")
        
    except ValueError as e:
        print(f"❌ 错误: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 获取项目状态失败: {e}")
        sys.exit(1)


def main():
    parser = argparse.ArgumentParser(
        description="项目创建和管理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:

  # 创建新项目
  python src/create_project.py --create co-la-data --center 34.0522,-118.2437 --radius 25 --desc "洛杉矶便利店扫描"
  
  # 列出所有项目
  python src/create_project.py --list
  
  # 生成网格
  python src/create_project.py --generate-grids co-la-data
  
  # 查看项目状态
  python src/create_project.py --status co-la-data

工作流程:
  1. 创建项目 → 2. 调整参数 → 3. 生成网格 → 4. 执行扫描 → 5. 查看结果
        """
    )
    
    # 通用参数
    parser.add_argument(
        '--projects-dir',
        default='projects',
        help='项目根目录 (默认: projects)'
    )
    
    # 操作参数 (互斥)
    action_group = parser.add_mutually_exclusive_group(required=True)
    
    action_group.add_argument(
        '--create',
        metavar='NAME',
        help='创建新项目'
    )
    
    action_group.add_argument(
        '--list',
        action='store_true',
        help='列出所有项目'
    )
    
    action_group.add_argument(
        '--generate-grids',
        metavar='PROJECT',
        help='为指定项目生成网格'
    )
    
    action_group.add_argument(
        '--status',
        metavar='PROJECT', 
        help='显示项目详细状态'
    )
    
    # 创建项目的参数
    parser.add_argument(
        '--center',
        metavar='LAT,LNG',
        help='中心坐标 (如: 34.0522,-118.2437)'
    )
    
    parser.add_argument(
        '--radius',
        type=float,
        help='扫描半径 (公里)'
    )
    
    parser.add_argument(
        '--desc',
        help='项目描述'
    )
    
    args = parser.parse_args()
    
    # 验证创建项目的参数
    if args.create:
        if not args.center or not args.radius:
            print("❌ 创建项目需要指定 --center 和 --radius 参数")
            print("   示例: --center 34.0522,-118.2437 --radius 25")
            sys.exit(1)
        
        try:
            center_parts = args.center.split(',')
            if len(center_parts) != 2:
                raise ValueError("中心坐标格式错误")
            args.center_lat = float(center_parts[0])
            args.center_lng = float(center_parts[1])
        except ValueError:
            print("❌ 中心坐标格式错误，应为: 纬度,经度 (如: 34.0522,-118.2437)")
            sys.exit(1)
        
        if not (-90 <= args.center_lat <= 90):
            print("❌ 纬度必须在 -90 到 90 之间")
            sys.exit(1)
        
        if not (-180 <= args.center_lng <= 180):
            print("❌ 经度必须在 -180 到 180 之间")
            sys.exit(1)
        
        if args.radius <= 0 or args.radius > 100:
            print("❌ 扫描半径必须在 0 到 100 公里之间")
            sys.exit(1)
        
        args.name = args.create
        args.description = args.desc
        create_project(args)
    
    elif args.list:
        list_projects(args)
    
    elif args.generate_grids:
        args.project = args.generate_grids
        generate_grids(args)
    
    elif args.status:
        args.project = args.status
        show_project_status(args)


if __name__ == "__main__":
    main() 