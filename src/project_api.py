#!/usr/bin/env python3
"""
项目管理API服务器 - 为可视化界面提供后端支持

提供的API端点：
- GET /api/projects - 列出所有项目
- POST /api/projects - 创建新项目
- GET /api/projects/{name} - 获取项目详情
- PUT /api/projects/{name} - 更新项目配置
- GET /api/projects/{name}/grids - 获取项目网格数据
- POST /api/projects/{name}/grids - 生成项目网格
- POST /api/projects/{name}/execute - 执行指定网格
"""

import json
import os
import sys
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading
import webbrowser
from typing import Dict, Any, List
import traceback
from pathlib import Path
from datetime import datetime

# 添加src路径到模块搜索路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.project_manager import ProjectManager, ProjectConfig


class ProjectAPIHandler(BaseHTTPRequestHandler):
    """处理项目管理API请求"""
    
    def __init__(self, *args, **kwargs):
        self.project_manager = ProjectManager()
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        try:
            parsed_url = urlparse(self.path)
            path = parsed_url.path
            
            if path == '/':
                self._serve_file('src/project_visualizer.html')
            elif path == '/api/projects':
                self._handle_list_projects()
            elif path.startswith('/api/projects/') and path.endswith('/grids'):
                project_name = path.split('/')[3]
                self._handle_get_project_grids(project_name)
            elif path.startswith('/api/projects/'):
                project_name = path.split('/')[3]
                self._handle_get_project(project_name)
            elif path.startswith('/results/'):
                project_name = path.split('/')[2]
                self._handle_view_results(project_name)
            else:
                self._send_error(404, 'Not Found')
                
        except Exception as e:
            print(f"GET请求处理错误: {e}")
            traceback.print_exc()
            self._send_error(500, str(e))
    
    def do_POST(self):
        """处理POST请求"""
        try:
            parsed_url = urlparse(self.path)
            path = parsed_url.path
            
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length).decode('utf-8')
            data = json.loads(post_data) if content_length > 0 else {}
            
            if path == '/api/projects':
                self._handle_create_project(data)
            elif path.endswith('/grids'):
                project_name = path.split('/')[3]
                self._handle_generate_grids(project_name, data)
            elif path.endswith('/execute'):
                project_name = path.split('/')[3]
                self._handle_execute_grids(project_name, data)
            elif path.endswith('/export'):
                project_name = path.split('/')[3]
                self._handle_export_data(project_name, data)
            else:
                self._send_error(404, 'Not Found')
                
        except Exception as e:
            print(f"POST请求处理错误: {e}")
            traceback.print_exc()
            self._send_error(500, str(e))
    
    def do_PUT(self):
        """处理PUT请求"""
        try:
            parsed_url = urlparse(self.path)
            path = parsed_url.path
            
            content_length = int(self.headers.get('Content-Length', 0))
            put_data = self.rfile.read(content_length).decode('utf-8')
            data = json.loads(put_data) if content_length > 0 else {}
            
            if path.startswith('/api/projects/'):
                project_name = path.split('/')[3]
                self._handle_update_project(project_name, data)
            else:
                self._send_error(404, 'Not Found')
                
        except Exception as e:
            print(f"PUT请求处理错误: {e}")
            traceback.print_exc()
            self._send_error(500, str(e))
    
    def do_OPTIONS(self):
        """处理CORS预检请求"""
        self.send_response(200)
        self._set_cors_headers()
        self.end_headers()
    
    def _set_cors_headers(self):
        """设置CORS头"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
    
    def _send_json_response(self, data: Any, status_code: int = 200):
        """发送JSON响应"""
        try:
            self.send_response(status_code)
            self.send_header('Content-Type', 'application/json; charset=utf-8')
            self._set_cors_headers()
            self.end_headers()
            
            json_data = json.dumps(data, ensure_ascii=False, indent=2)
            self.wfile.write(json_data.encode('utf-8'))
        except BrokenPipeError:
            # 客户端连接已断开，静默处理
            print("🔗 客户端连接已断开，跳过响应发送")
        except Exception as e:
            print(f"❌ 发送响应失败: {e}")
    
    def _send_error(self, status_code: int, message: str):
        """发送错误响应"""
        try:
            self._send_json_response({
                'error': True,
                'message': message,
                'status_code': status_code
            }, status_code)
        except Exception:
            # 如果发送错误响应也失败，就静默处理
            print(f"❌ 无法发送错误响应: {message}")
    
    def _serve_file(self, filepath: str):
        """提供静态文件服务"""
        try:
            if not os.path.exists(filepath):
                self._send_error(404, 'File not found')
                return
            
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.send_response(200)
            
            if filepath.endswith('.html'):
                self.send_header('Content-Type', 'text/html; charset=utf-8')
            elif filepath.endswith('.js'):
                self.send_header('Content-Type', 'application/javascript; charset=utf-8')
            elif filepath.endswith('.css'):
                self.send_header('Content-Type', 'text/css; charset=utf-8')
            else:
                self.send_header('Content-Type', 'text/plain; charset=utf-8')
            
            self._set_cors_headers()
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
            
        except Exception as e:
            self._send_error(500, f'文件服务错误: {str(e)}')
    
    def _handle_list_projects(self):
        """处理列出项目请求"""
        try:
            projects = self.project_manager.list_projects()
            self._send_json_response(projects)
        except Exception as e:
            self._send_error(500, f'获取项目列表失败: {str(e)}')
    
    def _handle_create_project(self, data: Dict[str, Any]):
        """处理创建项目请求"""
        try:
            required_fields = ['name', 'center_lat', 'center_lng', 'radius']
            for field in required_fields:
                if field not in data:
                    self._send_error(400, f'缺少必填字段: {field}')
                    return
            
            project_dir = self.project_manager.create_project(
                name=data['name'],
                center_lat=data['center_lat'],
                center_lng=data['center_lng'],
                scan_radius=data['radius'],
                description=data.get('description', '')
            )
            
            self._send_json_response({
                'success': True,
                'message': f'项目 {data["name"]} 创建成功',
                'project_dir': project_dir
            })
            
        except ValueError as e:
            self._send_error(400, str(e))
        except Exception as e:
            self._send_error(500, f'创建项目失败: {str(e)}')
    
    def _handle_get_project(self, project_name: str):
        """处理获取项目详情请求"""
        try:
            project_dir, config = self.project_manager.load_project(project_name)
            stats = self.project_manager.get_project_stats(project_name)
            
            # 转换配置为字典格式
            from dataclasses import asdict
            project_data = asdict(config)
            project_data['stats'] = stats
            project_data['project_dir'] = project_dir
            
            self._send_json_response(project_data)
            
        except ValueError as e:
            self._send_error(404, str(e))
        except Exception as e:
            self._send_error(500, f'获取项目失败: {str(e)}')
    
    def _handle_update_project(self, project_name: str, data: Dict[str, Any]):
        """处理更新项目配置请求"""
        try:
            self.project_manager.update_project_config(project_name, **data)
            
            self._send_json_response({
                'success': True,
                'message': f'项目 {project_name} 配置已更新'
            })
            
        except ValueError as e:
            self._send_error(404, str(e))
        except Exception as e:
            self._send_error(500, f'更新项目失败: {str(e)}')
    
    def _handle_get_project_grids(self, project_name: str):
        """处理获取项目网格数据请求"""
        try:
            grids = self.project_manager.get_project_grids(project_name)
            self._send_json_response(grids)
        except ValueError as e:
            self._send_error(404, str(e))
        except Exception as e:
            self._send_error(500, f'获取网格数据失败: {str(e)}')
    
    def _handle_view_results(self, project_name: str):
        """处理查看项目结果请求"""
        try:
            # 验证项目存在
            project_dir, config = self.project_manager.load_project(project_name)
            
            # 获取项目统计信息
            stats = self.project_manager.get_project_stats(project_name)
            
            # 读取结果目录中的文件
            results_dir = Path(project_dir) / "results"
            result_files = []
            
            if results_dir.exists():
                for file_path in results_dir.glob("*_results.json"):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            result_files.append({
                                'grid_id': data.get('grid_id', file_path.stem.replace('_results', '')),
                                'scan_time': data.get('scan_time', ''),
                                'places_count': data.get('places_count', 0),
                                'places': data.get('places', [])
                            })
                    except Exception as e:
                        print(f"读取结果文件失败 {file_path}: {e}")
            
            # 生成结果页面HTML
            html_content = self._generate_results_page(project_name, config, stats, result_files)
            
            self.send_response(200)
            self.send_header('Content-Type', 'text/html; charset=utf-8')
            self._set_cors_headers()
            self.end_headers()
            self.wfile.write(html_content.encode('utf-8'))
            
        except ValueError as e:
            self._send_error(404, str(e))
        except Exception as e:
            self._send_error(500, f'获取结果失败: {str(e)}')
    
    def _generate_results_page(self, project_name: str, config, stats: Dict, result_files: List) -> str:
        """生成结果查看页面"""
        total_places = sum(rf['places_count'] for rf in result_files)
        
        places_html = ""
        for rf in result_files:
            if rf['places']:
                places_html += f"<h4>Grid {rf['grid_id']} ({rf['places_count']} 个地点)</h4>"
                places_html += "<ul>"
                for place in rf['places'][:10]:  # 只显示前10个
                    places_html += f"<li><strong>{place.get('name', 'Unknown')}</strong> - {place.get('vicinity', '')}</li>"
                if len(rf['places']) > 10:
                    places_html += f"<li><em>... 还有 {len(rf['places']) - 10} 个地点</em></li>"
                places_html += "</ul>"
        
        if not places_html:
            places_html = "<p style='text-align: center; color: #666; margin: 40px 0;'>暂无扫描结果</p>"
        
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>项目结果 - {project_name}</title>
            <style>
                body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif; margin: 0; padding: 20px; background: #f8f9fa; }}
                .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .header {{ text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #eee; }}
                .stats {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }}
                .stat-card {{ background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }}
                .stat-value {{ font-size: 2em; font-weight: bold; color: #667eea; }}
                .stat-label {{ color: #666; font-size: 0.9em; }}
                .content {{ margin-top: 30px; }}
                .content h4 {{ color: #333; border-bottom: 1px solid #eee; padding-bottom: 10px; }}
                .content ul {{ list-style-type: none; padding: 0; }}
                .content li {{ padding: 8px 0; border-bottom: 1px solid #f0f0f0; }}
                .back-btn {{ display: inline-block; padding: 10px 20px; background: #667eea; color: white; text-decoration: none; border-radius: 6px; margin-bottom: 20px; }}
                .back-btn:hover {{ background: #5a6fd8; }}
            </style>
        </head>
        <body>
            <div class="container">
                <a href="/" class="back-btn">← 返回项目</a>
                
                <div class="header">
                    <h1>📊 项目结果: {project_name}</h1>
                    <p>{config.description}</p>
                </div>
                
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-value">{stats['total_grids']}</div>
                        <div class="stat-label">总网格数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">{stats['by_status'].get('completed', 0)}</div>
                        <div class="stat-label">已完成</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">{total_places}</div>
                        <div class="stat-label">发现地点</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${stats['total_cost']:.2f}</div>
                        <div class="stat-label">已花费</div>
                    </div>
                </div>
                
                <div class="content">
                    <h3>🏪 发现的地点</h3>
                    {places_html}
                </div>
            </div>
        </body>
        </html>
        """
    
    def _handle_generate_grids(self, project_name: str, data: Dict[str, Any]):
        """处理生成网格请求"""
        try:
            regenerate = data.get('regenerate', False)
            
            if regenerate:
                # 重新生成：先清理现有网格
                print(f"🔄 重新生成项目 {project_name} 的网格...")
                self.project_manager.clear_project_grids(project_name)
            
            stats = self.project_manager.generate_grids(project_name)
            
            action = "重新生成" if regenerate else "生成"
            
            self._send_json_response({
                'success': True,
                'message': f'项目 {project_name} 网格{action}完成',
                'stats': stats,
                'regenerated': regenerate
            })
            
        except ValueError as e:
            print(f"ERROR in _handle_generate_grids: Caught ValueError: {e}")
            traceback.print_exc() # Print full traceback for ValueError
            self._send_error(404, str(e))
        except Exception as e:
            self._send_error(500, f'生成网格失败: {str(e)}')
    
    def _handle_execute_grids(self, project_name: str, data: Dict[str, Any]):
        """处理执行网格请求"""
        try:
            grid_ids = data.get('grid_ids', [])
            execute_all = data.get('execute_all', False)
            mock_mode = data.get('mock_mode', True)  # 默认使用模拟模式
            
            if not grid_ids and not execute_all:
                self._send_error(400, '需要指定grid_ids或设置execute_all')
                return
            
            print(f"🎯 执行网格请求: 项目={project_name}, 模式={'模拟' if mock_mode else '真实API'}")
            
            # 检查客户端连接状态
            try:
                # 发送一个小的数据包测试连接
                self.wfile.flush()
            except (BrokenPipeError, ConnectionResetError):
                print("🔗 客户端连接已断开，取消执行")
                return
            
            # 使用GridExecutor执行网格
            from src.grid_executor import GridExecutor
            
            executor = GridExecutor(project_name, mock_mode=mock_mode)
            
            if execute_all:
                # 执行所有待处理Grid
                print("⚡ 开始批量执行所有待处理Grid...")
                results = executor.execute_all_pending()
                message = f'批量执行完成: {len(results)} 个Grid'
                print(f"📊 批量执行结果: {len(results)} 个Grid")
            else:
                # 执行指定Grid
                grid_specs = []
                for grid_id in grid_ids:
                    # 从项目管理器获取Grid级别
                    grid_found = False
                    for level in [1, 2, 3]:
                        if self.project_manager.get_grid_status(project_name, level, grid_id):
                            grid_specs.append({'grid_id': grid_id, 'level': level})
                            grid_found = True
                            break
                    
                    if not grid_found:
                        # 简化回退：从Grid ID推断级别
                        level = 1
                        if '_L2_' in grid_id or '_2_' in grid_id:
                            level = 2
                        elif '_L3_' in grid_id or '_3_' in grid_id:
                            level = 3
                        grid_specs.append({'grid_id': grid_id, 'level': level})
                
                print(f"⚡ 开始执行指定的 {len(grid_specs)} 个Grid...")
                results = executor.execute_grids_batch(grid_specs)
                message = f'指定Grid执行完成: {len(results)} 个Grid'
                print(f"📊 指定Grid执行结果: {len(results)} 个Grid")
            
            # 再次检查连接状态
            try:
                self.wfile.flush()
            except (BrokenPipeError, ConnectionResetError):
                print("🔗 执行完成但客户端连接已断开，跳过响应发送")
                print(f"📈 执行统计: 总计={len(results)} 个Grid (连接断开，未发送响应)")
                return
            
            # 统计结果
            successful = sum(1 for r in results if r.status == "completed")
            failed = sum(1 for r in results if r.status == "failed")
            total_places = sum(r.places_found for r in results)
            total_cost = sum(r.cost_spent for r in results)
            triggered_next_level_count = sum(1 for r in results if r.triggered_next_level)
            
            print(f"📈 执行统计: 成功={successful}, 失败={failed}, 地点={total_places}, 费用=${total_cost:.3f}")
            if triggered_next_level_count > 0:
                print(f"🎯 {triggered_next_level_count} 个Grid触发了下一级网格生成")
            
            # 限制响应数据大小，避免超大响应导致连接问题
            limited_results = []
            for r in results[:50]:  # 只返回前50个详细结果
                result_dict = r.to_dict()
                # 移除可能很大的字段
                if 'execution_details' in result_dict:
                    del result_dict['execution_details']
                limited_results.append(result_dict)
            
            result = {
                'success': True,
                'message': message,
                'successful': successful,
                'failed': failed,
                'total_places': total_places,
                'total_cost': total_cost,
                'triggered_next_level_count': triggered_next_level_count,
                'mock_mode': mock_mode,
                'stats': {
                    'total_executed': len(results),
                    'successful': successful,
                    'failed': failed,
                    'total_places': total_places,
                    'total_cost': total_cost,
                    'triggered_next_level': triggered_next_level_count
                },
                'results': limited_results,
                'results_truncated': len(results) > 50,
                'total_results': len(results)
            }
            
            self._send_json_response(result)
            
        except (BrokenPipeError, ConnectionResetError) as e:
            print(f"🔗 连接错误，客户端可能已断开: {e}")
        except ValueError as e:
            print(f"❌ 执行网格参数错误: {e}")
            self._send_error(404, str(e))
        except Exception as e:
            print(f"❌ 执行网格失败: {e}")
            import traceback
            traceback.print_exc()
            self._send_error(500, f'执行网格失败: {str(e)}')
    
    def _handle_export_data(self, project_name: str, data: Dict[str, Any]):
        """处理数据导出请求"""
        try:
            print(f"🔄 开始导出项目 {project_name} 的数据...")
            
            # 导入数据导出器
            from src.data_exporter import DataExporter
            
            # 创建导出器并执行导出
            exporter = DataExporter(project_name)
            export_files = exporter.export_all_formats()
            
            if not export_files:
                self._send_error(404, '没有找到可导出的数据')
                return
            
            # 收集导出统计信息
            stats = self._get_export_stats(project_name)
            
            self._send_json_response({
                'success': True,
                'message': f'项目 {project_name} 数据导出完成',
                'export_files': list(export_files.values()),
                'total_places': stats.get('total_places', 0),
                'total_grids': stats.get('total_grids', 0),
                'successful_grids': stats.get('successful_grids', 0),
                'export_time': stats.get('export_time')
            })
            
        except Exception as e:
            print(f"❌ 数据导出失败: {e}")
            import traceback
            traceback.print_exc()
            self._send_error(500, f'数据导出失败: {str(e)}')
    
    def _get_export_stats(self, project_name: str) -> Dict[str, Any]:
        """获取导出统计信息"""
        try:
            from src.data_exporter import DataExporter
            
            exporter = DataExporter(project_name)
            all_places, stats = exporter._collect_all_results()
            
            return {
                'total_places': len(all_places),
                'total_grids': stats.get('total_grids', 0),
                'successful_grids': stats.get('successful_grids', 0),
                'export_time': datetime.now().isoformat()
            }
        except Exception:
            return {
                'total_places': 0,
                'total_grids': 0,
                'successful_grids': 0,
                'export_time': datetime.now().isoformat()
            }
    
    def log_message(self, format, *args):
        """自定义日志消息格式"""
        print(f"[{self.log_date_time_string()}] {format % args}")


class ProjectAPIServer:
    """项目管理API服务器"""
    
    def __init__(self, host='localhost', port=8092):
        self.host = host
        self.port = port
        self.httpd = None
        self.server_thread = None
    
    def start(self):
        """启动服务器"""
        try:
            self.httpd = HTTPServer((self.host, self.port), ProjectAPIHandler)
            
            print(f"🚀 项目管理服务器启动中...")
            print(f"📍 服务器地址: http://{self.host}:{self.port}")
            print(f"🌐 浏览器将自动打开，如果没有打开请手动访问上述地址")
            print(f"💡 使用 Ctrl+C 停止服务器")
            print("-" * 60)
            
            # 自动打开浏览器
            if self.host in ['localhost', '127.0.0.1']:
                webbrowser.open(f"http://{self.host}:{self.port}")
            
            # 启动服务器
            self.httpd.serve_forever()
            
        except OSError as e:
            if "Address already in use" in str(e):
                print(f"❌ 端口 {self.port} 已被占用")
                print(f"💡 请尝试其他端口或关闭占用该端口的程序")
            else:
                print(f"❌ 启动服务器失败: {e}")
        except KeyboardInterrupt:
            print(f"\n✅ 服务器已停止")
            self.stop()
    
    def start_background(self):
        """在后台线程启动服务器"""
        self.server_thread = threading.Thread(target=self.start, daemon=True)
        self.server_thread.start()
        return self.server_thread
    
    def stop(self):
        """停止服务器"""
        if self.httpd:
            self.httpd.shutdown()
            self.httpd.server_close()
        
        if self.server_thread and self.server_thread.is_alive():
            self.server_thread.join(timeout=1)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="项目管理API服务器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用说明:
  这个服务器为项目可视化界面提供后端API支持，包括：
  
  📋 项目管理:
    • 创建、列出、更新项目
    • 获取项目详情和统计
  
  🔧 网格管理:
    • 生成和获取网格数据
    • 可视化网格状态
  
  🚀 执行控制:
    • 执行指定网格
    • 批量执行和状态跟踪
  
  🌐 可视化界面:
    • 交互式地图界面
    • 实时参数调节
    • Grid选择和执行

示例:
  python src/project_api.py
  python src/project_api.py --port 8080 --host 0.0.0.0
        """
    )
    
    parser.add_argument(
        '--host',
        default='localhost',
        help='服务器主机地址 (默认: localhost)'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=8092,
        help='服务器端口 (默认: 8092)'
    )
    
    args = parser.parse_args()
    
    print("🎯 项目管理API服务器")
    print("=" * 40)
    print("提供项目创建、网格管理、执行控制的完整API")
    print()
    
    server = ProjectAPIServer(args.host, args.port)
    server.start()


if __name__ == "__main__":
    main() 