import os
import requests
import time
import json
import random
import hashlib
from datetime import datetime, timedelta
from typing import Optional, List, Dict, TYPE_CHECKING

if TYPE_CHECKING:
    from .models import GridPoint
from src.models import Coordinate, PlaceData
from src.config import ScanConfig

class APIRateLimitError(Exception):
    """自定义异常：API速率限制错误"""
    def __init__(self, retry_after: int = None):
        self.retry_after = retry_after
        super().__init__(f"API速率限制，建议等待 {retry_after} 秒" if retry_after else "API速率限制")

class APIQuotaExceededError(Exception):
    """自定义异常：API配额超出错误"""
    pass

class PlacesAPIClient:
    """
    Enhanced client for interacting with the Google Places API with intelligent rate limiting.
    """
    
    def __init__(self, config: ScanConfig):
        """
        Initializes the PlacesAPIClient with enhanced error handling and validation.

        Args:
            config: An instance of ScanConfig containing API parameters.
        
        Raises:
            ValueError: If the GOOGLE_PLACES_API_KEY environment variable is not set.
        """
        self.config = config
        self.api_key = os.getenv('GOOGLE_PLACES_API_KEY')
        
        # 在模拟模式下，不需要真实的API密钥
        if not self.api_key and not config.mock_mode:
            raise ValueError("GOOGLE_PLACES_API_KEY environment variable not set.")
        
        self.base_url = "https://places.googleapis.com/v1/places:searchNearby"
        
        # Rate limiting state
        self._last_request_time = 0
        self._request_count = 0
        self._window_start = time.time()
        self._rate_limit_window = 60  # 1 minute window
        self._max_requests_per_window = 1000  # Conservative limit
        
        # API validation
        self._api_key_validated = False
        self._quota_exhausted = False
        
        # Performance tracking
        self._response_times = []
        self._success_count = 0
        self._error_count = 0
        
        # Mock mode configuration
        self.mock_mode = config.mock_mode
        if self.mock_mode:
            print("🎭 启用模拟模式 - 将生成模拟数据而非真实API调用")
            self._api_key_validated = True  # 在模拟模式下跳过API密钥验证
            self._init_mock_data_templates()

    def _validate_api_key(self):
        """
        Validates the API key with a simple test request.
        In mock mode, skips validation.
        """
        if self._api_key_validated or self.mock_mode:
            if self.mock_mode:
                print("🎭 模拟模式：跳过API密钥验证")
                self._api_key_validated = True
            return
            
        try:
            # Make a minimal test request
            test_payload = {
                "includedTypes": ["establishment"],
                "maxResultCount": 1,
                "locationRestriction": {
                    "circle": {
                        "center": {"latitude": 34.0522, "longitude": -118.2437},
                        "radius": 100
                    }
                }
            }
            
            headers = {
                'Content-Type': 'application/json',
                'X-Goog-Api-Key': self.api_key,
                'X-Goog-FieldMask': "places.id"
            }
            
            response = requests.post(
                self.base_url, 
                json=test_payload, 
                headers=headers, 
                timeout=10
            )
            
            if response.status_code == 403:
                error_data = response.json()
                if "API_KEY_INVALID" in str(error_data):
                    raise ValueError("无效的Google Places API密钥")
                elif "QUOTA_EXCEEDED" in str(error_data):
                    raise APIQuotaExceededError("API配额已超出")
            
            response.raise_for_status()
            self._api_key_validated = True
            print("✅ API密钥验证成功")
            
        except requests.exceptions.RequestException as e:
            raise ValueError(f"API密钥验证失败: {e}")

    def _check_rate_limit(self):
        """
        Implements client-side rate limiting to prevent hitting API limits.
        """
        current_time = time.time()
        
        # Reset window if needed
        if current_time - self._window_start >= self._rate_limit_window:
            self._window_start = current_time
            self._request_count = 0
        
        # Check if we're approaching the limit
        if self._request_count >= self._max_requests_per_window * 0.9:  # 90% of limit
            wait_time = self._rate_limit_window - (current_time - self._window_start)
            if wait_time > 0:
                print(f"⚠️ 接近速率限制，等待 {wait_time:.1f} 秒...")
                time.sleep(wait_time)
                self._window_start = time.time()
                self._request_count = 0
        
        # Ensure minimum time between requests (avoid burst)
        min_interval = 0.1  # 100ms minimum between requests
        time_since_last = current_time - self._last_request_time
        if time_since_last < min_interval:
            time.sleep(min_interval - time_since_last)
        
        self._last_request_time = time.time()
        self._request_count += 1

    def _prepare_headers(self) -> Dict[str, str]:
        """Prepares the headers for the API request."""
        return {
            'Content-Type': 'application/json',
            'X-Goog-Api-Key': self.api_key,
            'X-Goog-FieldMask': ",".join(self.config.RESPONSE_FIELDS),
            'User-Agent': 'grid-map-scanner/1.0'
        }

    def _prepare_payload(self, center: Coordinate, radius: int) -> Dict:
        """Prepares the JSON payload for the Nearby Search request."""
        return {
            "includedTypes": self.config.PLACE_TYPES,
            "maxResultCount": 20,
            "locationRestriction": {
                "circle": {
                    "center": {
                        "latitude": center.latitude,
                        "longitude": center.longitude
                    },
                    "radius": radius
                }
            }
        }

    def _make_api_request(self, center: Coordinate, radius: int) -> requests.Response:
        """
        Makes the actual HTTP POST request to the Google Places API with enhanced error handling.

        Args:
            center: The center coordinate for the search.
            radius: The search radius in meters.

        Returns:
            The Response object from the requests library.
        """
        # Validate API key on first request
        if not self._api_key_validated:
            self._validate_api_key()
        
        # Check quota status
        if self._quota_exhausted:
            raise APIQuotaExceededError("API配额已超出，请检查您的Google Cloud账户")
        
        # Apply rate limiting
        self._check_rate_limit()
        
        headers = self._prepare_headers()
        payload = self._prepare_payload(center, radius)
        
        start_time = time.time()
        
        try:
            response = requests.post(
                self.base_url,
                json=payload,
                headers=headers,
                timeout=self.config.API_TIMEOUT
            )
            
            # Track response time
            response_time = time.time() - start_time
            self._response_times.append(response_time)
            if len(self._response_times) > 100:  # Keep only last 100 measurements
                self._response_times.pop(0)
            
            # Handle specific error codes
            if response.status_code == 429:
                retry_after = int(response.headers.get('Retry-After', 60))
                raise APIRateLimitError(retry_after)
            elif response.status_code == 403:
                error_data = response.json()
                if "QUOTA_EXCEEDED" in str(error_data):
                    self._quota_exhausted = True
                    raise APIQuotaExceededError("API配额已超出")
            
            response.raise_for_status()  # Raise an exception for bad status codes (4xx or 5xx)
            self._success_count += 1
            return response
            
        except requests.exceptions.Timeout:
            self._error_count += 1
            raise requests.exceptions.RequestException(f"API请求超时 ({self.config.API_TIMEOUT}秒)")
        except requests.exceptions.RequestException as e:
            self._error_count += 1
            raise e

    def _extract_places(self, response_data: dict, grid_point_id: str, scan_level: int) -> List[PlaceData]:
        """
        Extracts and transforms place data from the API response with enhanced validation.

        Args:
            response_data: The JSON response data from the API.
            grid_point_id: The ID of the grid point that this search originated from.
            scan_level: The scan level of the search.

        Returns:
            A list of PlaceData objects.
        """
        places = response_data.get('places', [])
        extracted_data = []
        
        for place in places:
            try:
                # Validate required fields
                if not place.get('id'):
                    continue
                    
                place_data = PlaceData(
                    place_id=place.get('id', ''),
                    name=place.get('displayName', {}).get('text', 'Unknown'),
                    types=place.get('types', []),
                    formatted_address=place.get('formattedAddress', ''),
                    latitude=place.get('location', {}).get('latitude', 0.0),
                    longitude=place.get('location', {}).get('longitude', 0.0),
                    postal_address=place.get('postalAddress', ''),
                    photos=[photo.get('name', '') for photo in place.get('photos', [])],
                    grid_point_id=grid_point_id,
                    scan_time=datetime.now().isoformat(),
                    scan_level=scan_level
                )
                extracted_data.append(place_data)
                
            except (KeyError, TypeError, ValueError) as e:
                print(f"Warning: 跳过格式不正确的地点数据: {e}")
                continue
        
        return extracted_data

    def nearby_search(self, grid_point: "GridPoint") -> Optional[List[PlaceData]]:
        """
        Executes a Nearby Search API call with enhanced retry logic and rate limiting.
        In mock mode, returns simulated data instead of making real API calls.

        Args:
            grid_point: The GridPoint to search around.

        Returns:
            A list of PlaceData objects if successful, otherwise None.
        """
        # 如果启用了模拟模式，返回模拟数据
        if self.mock_mode:
            try:
                self._simulate_api_delay()
                mock_places = self._generate_mock_places(grid_point)
                self._success_count += 1
                print(f"🎭 模拟API调用 - 网格点 {grid_point.id}: 生成了 {len(mock_places)} 个地点")
                return mock_places
            except Exception as e:
                self._error_count += 1
                print(f"模拟数据生成失败: {e}")
                return None
        
        # 真实API调用逻辑
        for attempt in range(self.config.MAX_RETRIES):
            try:
                response = self._make_api_request(grid_point.center, grid_point.radius)
                places = self._extract_places(response.json(), grid_point.id, grid_point.level)
                return places
                
            except APIRateLimitError as e:
                wait_time = e.retry_after or (self.config.RETRY_DELAY * (2 ** attempt))
                print(f"API速率限制 (尝试 {attempt + 1}/{self.config.MAX_RETRIES}): 等待 {wait_time} 秒")
                if attempt < self.config.MAX_RETRIES - 1:
                    time.sleep(wait_time)
                else:
                    print(f"在 {self.config.MAX_RETRIES} 次重试后仍遇到速率限制，跳过此网格点")
                    return None
                    
            except APIQuotaExceededError as e:
                print(f"API配额已超出: {e}")
                raise  # Don't retry quota errors
                
            except requests.exceptions.RequestException as e:
                print(f"API请求失败 (尝试 {attempt + 1}/{self.config.MAX_RETRIES}): {e}")
                if attempt < self.config.MAX_RETRIES - 1:
                    # Exponential backoff with jitter
                    base_delay = self.config.RETRY_DELAY * (2 ** attempt)
                    jitter = base_delay * 0.1 * (2 * time.time() % 1 - 1)  # ±10% jitter
                    wait_time = base_delay + jitter
                    time.sleep(max(wait_time, 0.1))
                else:
                    return None # All retries failed
                    
            except Exception as e:
                print(f"API调用时发生意外错误 (尝试 {attempt + 1}): {e}")
                return None
                
        return None

    def get_performance_stats(self) -> Dict[str, any]:
        """
        Returns current performance statistics.
        """
        avg_response_time = sum(self._response_times) / len(self._response_times) if self._response_times else 0
        total_requests = self._success_count + self._error_count
        success_rate = (self._success_count / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'total_requests': total_requests,
            'success_count': self._success_count,
            'error_count': self._error_count,
            'success_rate': success_rate,
            'avg_response_time': avg_response_time,
            'requests_this_window': self._request_count,
            'api_key_validated': self._api_key_validated,
            'quota_exhausted': self._quota_exhausted
        }

    def _init_mock_data_templates(self):
        """
        初始化模拟数据模板，用于生成真实感的测试数据。
        """
        self.mock_templates = {
            'convenience_store': [
                "7-Eleven", "FamilyMart", "Lawson", "Circle K", "Wawa", "Cumberland Farms",
                "Casey's General Store", "QuikTrip", "Speedway", "Shell Select"
            ],
            'supermarket': [
                "Walmart Supercenter", "Target", "Kroger", "Safeway", "Publix", "H-E-B",
                "Meijer", "Food Lion", "Giant Eagle", "Wegmans"
            ],
            'grocery_store': [
                "Whole Foods Market", "Trader Joe's", "Aldi", "Fresh Market", "IGA",
                "Piggly Wiggly", "Harris Teeter", "Bi-Lo", "Food 4 Less", "Save-A-Lot"
            ],
            'gas_station': [
                "Shell", "ExxonMobil", "BP", "Chevron", "Texaco", "Sunoco",
                "Phillips 66", "Marathon", "Valero", "Citgo"
            ]
        }
        
        self.address_templates = [
            "{number} Main St", "{number} Oak Ave", "{number} First St", "{number} Park Rd",
            "{number} Market St", "{number} Broadway", "{number} Center Dr", "{number} Hill Ave"
        ]

    def _generate_deterministic_seed(self, grid_point_id: str) -> int:
        """
        基于网格点ID生成确定性种子，确保相同的网格点总是产生相同的模拟数据。
        """
        return int(hashlib.md5(grid_point_id.encode()).hexdigest()[:8], 16)

    def _generate_mock_places(self, grid_point: "GridPoint") -> List[PlaceData]:
        """
        为给定的网格点生成模拟地点数据。
        
        Args:
            grid_point: 要生成数据的网格点
            
        Returns:
            模拟的PlaceData对象列表
        """
        # 使用确定性种子确保一致的结果
        seed = self._generate_deterministic_seed(grid_point.id)
        rng = random.Random(seed)
        
        # 根据扫描级别和半径决定地点数量
        if grid_point.level == 1:  # macro scan
            # 在模拟模式下，让一些网格点有高密度来触发递归
            if rng.random() < 0.3:  # 30%的概率是高密度热点
                base_count = 22  # 确保能触发递归（>20）
                variance = 5
            else:
                base_count = 8   # 普通密度
                variance = 6
        elif grid_point.level == 2:  # fine scan
            # 精细扫描也有机会触发增强扫描
            if rng.random() < 0.4:  # 40%的概率触发增强扫描
                base_count = 21  # 确保能触发递归
                variance = 4
            else:
                base_count = 12
                variance = 6
        else:  # enhanced scan
            base_count = 15
            variance = 8
        
        # 根据半径调整数量（半径越大，可能找到的地点越多）
        radius_factor = min(grid_point.radius / 1000.0, 3.0)  # 最多3倍
        adjusted_count = int(base_count * radius_factor)
        
        place_count = max(0, rng.randint(
            max(0, adjusted_count - variance), 
            adjusted_count + variance
        ))
        
        places = []
        center_lat = grid_point.center.latitude
        center_lng = grid_point.center.longitude
        
        for i in range(place_count):
            # 选择随机的地点类型
            place_type = rng.choice(self.config.PLACE_TYPES)
            
            # 生成随机位置（在搜索半径内）
            # 使用极坐标生成均匀分布的点
            r = grid_point.radius * (rng.random() ** 0.5)  # 平方根分布确保均匀
            theta = rng.random() * 2 * 3.14159
            
            # 转换为经纬度偏移 (粗略计算)
            lat_offset = (r * rng.choice([-1, 1]) * 0.000009) * rng.uniform(0.8, 1.2)
            lng_offset = (r * rng.choice([-1, 1]) * 0.000009) * rng.uniform(0.8, 1.2)
            
            place_lat = center_lat + lat_offset
            place_lng = center_lng + lng_offset
            
            # 生成地点名称和地址
            name_template = rng.choice(self.mock_templates.get(place_type, ["Generic Store"]))
            name = f"{name_template}"
            if rng.random() > 0.7:  # 30%的概率添加分店编号
                name += f" #{rng.randint(100, 999)}"
            
            address_template = rng.choice(self.address_templates)
            address = address_template.format(number=rng.randint(100, 9999))
            
            # 生成唯一的place_id
            place_id = f"mock_{place_type}_{grid_point.id}_{i:03d}"
            
            # 创建PlaceData对象
            place_data = PlaceData(
                place_id=place_id,
                name=name,
                types=[place_type, "establishment", "point_of_interest"],
                formatted_address=address,
                latitude=place_lat,
                longitude=place_lng,
                postal_address=address,
                photos=[f"mock_photo_{place_id}_{j}" for j in range(rng.randint(0, 3))],
                grid_point_id=grid_point.id,
                scan_time=datetime.now().isoformat(),
                scan_level=grid_point.level
            )
            
            places.append(place_data)
        
        return places

    def _simulate_api_delay(self):
        """
        模拟API调用延迟，使测试更真实。
        """
        # 模拟API响应时间 (50-200ms)
        delay = random.uniform(0.05, 0.2)
        time.sleep(delay)
        
        # 更新性能统计
        self._response_times.append(delay)
        if len(self._response_times) > 100:
            self._response_times.pop(0)
