
#!/usr/bin/env python3
"""
Grid执行器 - 支持单个和批量Grid的执行控制

提供功能：
- 单个Grid执行
- 批量Grid执行
- 执行状态跟踪
- 成本控制和监控
"""

import math
import h3
import json
import time
import random
import hashlib
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Set
import concurrent.futures
from dataclasses import asdict

from src.project_manager import ProjectManager, GridStatus
from src.places_client import PlacesAPIClient
from src.models import Coordinate, GridPoint
from src.config import ScanConfig


class GridExecutionResult:
    """Grid执行结果"""
    def __init__(self, h3_index: str, h3_res: int):
        self.h3_index = h3_index
        self.h3_res = h3_res
        self.status = "pending"
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        self.places_found = 0
        self.api_calls_made = 0
        self.cost_spent = 0.0
        self.error_message: Optional[str] = None
        self.triggered_next_level = False
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'h3_index': self.h3_index,
            'h3_res': self.h3_res,
            'status': self.status,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'places_found': self.places_found,
            'api_calls_made': self.api_calls_made,
            'cost_spent': self.cost_spent,
            'error_message': self.error_message,
            'triggered_next_level': self.triggered_next_level,
            'duration_seconds': (self.end_time - self.start_time).total_seconds() if self.start_time and self.end_time else None
        }


class GridExecutor:
    """Grid执行器 - 管理Grid的执行和状态更新"""
    
    def __init__(self, project_name: str, mock_mode: bool = False):
        self.project_name = project_name
        self.mock_mode = mock_mode
        self.project_manager = ProjectManager()
        
        # 加载项目配置
        self.project_dir, self.config = self.project_manager.load_project(project_name)
        
        # 初始化API客户端
        if not mock_mode:
            scan_config = self._create_scan_config()
            self.places_client = PlacesAPIClient(scan_config)
        else:
            self.places_client = None
        
        # 执行状态跟踪
        self.executing_grids: Set[str] = set()
        self.execution_results: Dict[str, GridExecutionResult] = {}
    
    def _create_scan_config(self) -> ScanConfig:
        """根据项目配置创建扫描配置"""
        return ScanConfig(
            center_latitude=self.config.center_latitude,
            center_longitude=self.config.center_longitude,
            scan_radius_km=self.config.scan_radius_km,
            # H3网格参数 - 与ProjectConfig保持一致
            H3_RES_LEVEL1=self.config.h3_res_level1,
            H3_RES_LEVEL2=self.config.h3_res_level2,
            H3_RES_LEVEL3=self.config.h3_res_level3,
            SEARCH_RADIUS_LEVEL1=self.config.search_radius_level1,
            SEARCH_RADIUS_LEVEL2=self.config.search_radius_level2,
            SEARCH_RADIUS_LEVEL3=self.config.search_radius_level3,
            RECURSION_TRIGGER_COUNT=self.config.recursion_trigger_count,
            MAX_BUDGET=self.config.max_budget,
            API_COST_PER_CALL=self.config.api_cost_per_call,
            PLACE_TYPES=self.config.place_types,
            mock_mode=self.mock_mode,
            skip_system_checks=True
        )
    
    def execute_grid(self, grid_spec: Dict[str, Any]) -> GridExecutionResult:
        """执行单个Grid"""
        h3_index = grid_spec['h3_index']
        h3_res = grid_spec['h3_res']
        
        print(f"🚀 开始执行 Grid {h3_index} (Res {h3_res})")
        
        result = GridExecutionResult(h3_index, h3_res)
        result.start_time = datetime.now()
        result.status = "running"
        
        self.executing_grids.add(h3_index)
        self.execution_results[h3_index] = result
        
        try:
            self.project_manager.update_grid_status(
                self.project_name, h3_res, h3_index,
                status="running",
                last_execution=result.start_time.isoformat()
            )
            
            grid_status = self.project_manager.get_grid_status(self.project_name, h3_res, h3_index)
            if not grid_status:
                raise ValueError(f"找不到Grid {h3_index}")
            
            if self.mock_mode:
                places_found = self._mock_search_places(grid_status)
            else:
                places_found = self._search_places(grid_status)
            
            result.places_found = len(places_found)
            result.api_calls_made = 1
            result.cost_spent = result.api_calls_made * self.config.api_cost_per_call
            
            if h3_res < self.config.h3_res_level3 and result.places_found >= self.config.recursion_trigger_count:
                result.triggered_next_level = True
                self._trigger_next_level(grid_status, places_found)
            
            result.status = "completed"
            result.end_time = datetime.now()
            
            self.project_manager.update_grid_status(
                self.project_name, h3_res, h3_index,
                status="completed",
                places_found=result.places_found,
                triggered_next_level=result.triggered_next_level,
                api_calls_made=result.api_calls_made,
                cost_spent=result.cost_spent
            )
            
            print(f"✅ Grid {h3_index} 执行完成: 发现 {result.places_found} 个地点")
            
        except Exception as e:
            result.status = "failed"
            result.error_message = str(e)
            result.end_time = datetime.now()
            
            self.project_manager.update_grid_status(
                self.project_name, h3_res, h3_index,
                status="failed",
                error_message=result.error_message
            )
            
            print(f"❌ Grid {h3_index} 执行失败: {e}")
        
        finally:
            self.executing_grids.discard(h3_index)
        
        return result
    
    def execute_grids_batch(self, grid_specs: List[Dict[str, Any]], max_workers: int = 3) -> List[GridExecutionResult]:
        """批量执行多个Grid"""
        print(f"🚀 开始批量执行 {len(grid_specs)} 个Grid")
        
        results = []
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_grid = {
                executor.submit(self.execute_grid, spec): spec
                for spec in grid_specs
            }
            
            for future in concurrent.futures.as_completed(future_to_grid):
                spec = future_to_grid[future]
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    print(f"❌ Grid {spec['h3_index']} 执行异常: {e}")
                    result = GridExecutionResult(spec['h3_index'], spec['h3_res'])
                    result.status = "failed"
                    result.error_message = str(e)
                    result.start_time = result.end_time = datetime.now()
                    results.append(result)
        
        successful = sum(1 for r in results if r.status == "completed")
        failed = sum(1 for r in results if r.status == "failed")
        total_places = sum(r.places_found for r in results)
        total_cost = sum(r.cost_spent for r in results)
        
        print(f"📊 批量执行完成: ✅{successful} ❌{failed} 📍{total_places}个地点 💰${total_cost:.2f}")
        
        return results
    
    def execute_all_pending(self, max_workers: int = 3) -> List[GridExecutionResult]:
        """执行所有待处理的Grid"""
        pending_grids = self.project_manager.get_grids_by_status(self.project_name, "pending")
        
        if not pending_grids:
            print("📋 没有待执行的Grid")
            return []
        
        print(f"📋 发现 {len(pending_grids)} 个待执行Grid")
        
        
        return self.execute_grids_batch(pending_grids, max_workers)
    
    def _search_places(self, grid_status: GridStatus) -> List[Dict[str, Any]]:
        """使用真实API搜索地点"""
        if not self.places_client:
            raise ValueError("Places API客户端未初始化")

        grid_point = GridPoint(
            center=Coordinate(
                latitude=grid_status.latitude,
                longitude=grid_status.longitude
            ),
            radius=grid_status.search_radius,
            level=grid_status.h3_res
        )
        grid_point.id = grid_status.h3_index

        places_data = self.places_client.nearby_search(grid_point)
        
        if places_data is None:
            places = []
        else:
            places = [asdict(p) for p in places_data]

        self._save_grid_results(grid_status.h3_index, places)

        return places
    
    def _mock_search_places(self, grid_status: GridStatus) -> List[Dict[str, Any]]:
        """模拟搜索地点（用于测试）- 支持密集区域控制"""
        
        # 模拟延迟
        time.sleep(random.uniform(0.5, 2.0))
        
        # 基于网格ID生成一致的随机数种子，确保相同网格多次执行结果一致
        grid_hash = hashlib.md5(grid_status.h3_index.encode()).hexdigest()
        grid_seed = int(grid_hash[:8], 16)
        rng = random.Random(grid_seed)
        
        # 根据配置确定该网格的密度类型
        num_places = self._determine_mock_place_count(grid_status, rng)
        
        # 确定密度类型
        if num_places >= self.config.mock_high_density_places:
            area_type = "高密度"
        elif num_places >= self.config.mock_medium_density_places:
            area_type = "中密度"
        else:
            area_type = "低密度"
        
        # 生成地点数据
        places = []
        for i in range(num_places):
            # 在Grid周围生成随机坐标
            lat_offset, lng_offset = self._get_random_offset_in_radius(grid_status.search_radius, rng)
            
            place = {
                'place_id': f"mock_place_{grid_status.h3_index}_{i}",
                'name': f"Mock Store {i+1} ({area_type})",
                'formatted_address': f"Mock Address {i+1}, {area_type}区域",
                'latitude': grid_status.latitude + lat_offset,
                'longitude': grid_status.longitude + lng_offset,
                'types': rng.choice([
                    ['convenience_store'], ['supermarket'], ['gas_station'], ['grocery_store']
                ]),
                'grid_point_id': grid_status.h3_index,
                'scan_time': datetime.now().isoformat(),
                'scan_level': grid_status.h3_res,
                'mock_density_type': area_type,
            }
            places.append(place)
        
        # 打印密度信息用于调试
        if num_places >= self.config.recursion_trigger_count:
            print(f"🎯 模拟{area_type}区域 Grid {grid_status.h3_index}: {num_places}个地点 (将触发下级扫描)")
        else:
            print(f"📍 模拟{area_type}区域 Grid {grid_status.h3_index}: {num_places}个地点")
        
        # 保存模拟结果
        self._save_grid_results(grid_status.h3_index, places)
        
        return places
    
    def _get_random_offset_in_radius(self, radius_m: int, rng: random.Random) -> tuple[float, float]:
        """在给定半径内生成一个随机的经纬度偏移"""
        # Convert radius from meters to degrees (approximate)
        radius_deg = radius_m / 111111.0  # 1 degree is roughly 111.111 km
        
        r = radius_deg * (rng.random() ** 0.5)  # Use sqrt for uniform distribution
        theta = rng.random() * 2 * math.pi
        
        lat_offset = r * math.sin(theta)
        lon_offset = r * math.cos(theta)
        
        return lat_offset, lon_offset
    
    def _determine_mock_place_count(self, grid_status: GridStatus, rng: random.Random) -> int:
        """根据配置的密集区域比例确定地点数量"""
        # 基于网格坐标生成伪随机分布，确保一致性
        coord_hash = abs(hash((grid_status.latitude, grid_status.longitude))) % 1000
        density_roll = coord_hash / 1000.0  # 0.0 - 1.0
        
        # 根据配置的比例确定密度类型
        if density_roll < self.config.mock_high_density_ratio:
            # 高密度区域
            base = self.config.mock_high_density_places
            variation = rng.randint(-5, 10)  # 更大变化范围
        elif density_roll < (self.config.mock_high_density_ratio + self.config.mock_medium_density_ratio):
            # 中密度区域
            base = self.config.mock_medium_density_places
            variation = rng.randint(-3, 8)
        else:
            # 低密度区域
            base = self.config.mock_low_density_places
            variation = rng.randint(-2, 5)
        
        # Level 2/3 有轻微递减
        if grid_status.h3_res == self.config.h3_res_level2:
            base = int(base * 0.8)
        elif grid_status.h3_res == self.config.h3_res_level3:
            base = int(base * 0.6)
        
        return max(0, base + variation)
    
    def _save_grid_results(self, grid_id: str, places: List[Dict[str, Any]]):
        """保存Grid搜索结果"""
        results_dir = Path(self.project_dir) / "results"
        results_dir.mkdir(exist_ok=True)
        
        # 保存到以Grid ID命名的文件
        result_file = results_dir / f"{grid_id}_results.json"
        
        result_data = {
            'grid_id': grid_id,
            'scan_time': datetime.now().isoformat(),
            'places_count': len(places),
            'places': places
        }
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, indent=2, ensure_ascii=False)
    
    def _trigger_next_level(self, grid_status: GridStatus, places: List[Dict[str, Any]]):
        """触发下一级Grid生成"""
        places_count = len(places)
        
        if grid_status.h3_res >= self.config.h3_res_level3:
            print("⚠️ 已达到最大扫描级别，不再生成下一级")
            return
        
        try:
            # 使用ProjectManager动态生成子网格
            child_grids = self.project_manager.generate_child_grids(
                self.project_name,
                grid_status.h3_index,
                places_count
            )
            
            if child_grids:
                next_res = h3.get_resolution(child_grids[0]['h3_index'])
                print(f"✅ 为Grid {grid_status.h3_index} 成功生成 {len(child_grids)} 个 Level {next_res} 网格")
                
                # 更新父网格状态
                self.project_manager.update_grid_status(
                    self.project_name, 
                    grid_status.h3_res, 
                    grid_status.h3_index,
                    triggered_next_level=True,
                    child_grids=[g['h3_index'] for g in child_grids]
                )
            else:
                print(f"📋 Grid {grid_status.h3_index} 未满足触发条件，不生成下级网格")
                
        except Exception as e:
            print(f"❌ 生成下级网格失败: {e}")
            import traceback
            traceback.print_exc()
    
    def get_execution_status(self) -> Dict[str, Any]:
        """获取当前执行状态"""
        return {
            'project_name': self.project_name,
            'executing_grids': list(self.executing_grids),
            'execution_count': len(self.execution_results),
            'results': {grid_id: result.to_dict() for grid_id, result in self.execution_results.items()}
        }
    
    def stop_execution(self, grid_id: Optional[str] = None):
        """停止执行（预留接口）"""
        if grid_id:
            print(f"⏹️ 请求停止Grid {grid_id} 的执行")
            # 实际实现需要线程间通信机制
        else:
            print("⏹️ 请求停止所有Grid执行")
    
    def cleanup(self):
        """清理资源"""
        if self.places_client:
            # 如果有清理方法的话
            pass 