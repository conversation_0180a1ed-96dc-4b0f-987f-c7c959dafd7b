from dataclasses import dataclass, asdict, field
from typing import List, Dict, Any
import os
import shutil

@dataclass
class ScanConfig:
    """
    Configuration class for the Google Places API scanner with enhanced validation.
    """
    # H3 Grid Parameters - 与ProjectConfig保持一致
    H3_RES_LEVEL1: int = 5               # H3 resolution for Level 1 grids
    H3_RES_LEVEL2: int = 7               # H3 resolution for Level 2 grids
    H3_RES_LEVEL3: int = 8               # H3 resolution for Level 3 grids

    # Search Radius Parameters (in meters) - 与ProjectConfig保持一致
    SEARCH_RADIUS_LEVEL1: int = 5000     # Level 1 search radius in meters
    SEARCH_RADIUS_LEVEL2: int = 1000     # Level 2 search radius in meters
    SEARCH_RADIUS_LEVEL3: int = 500      # Level 3 search radius in meters

    # Recursion Parameters
    MAX_RECURSION_DEPTH: int = 3         # Maximum recursion depth for enhanced scans
    RECURSION_TRIGGER_COUNT: int = 20    # Number of places found to trigger recursion - 与ProjectConfig保持一致
    MIN_SEARCH_RADIUS: int = 200         # Minimum search radius in meters

    # 保留传统参数以兼容旧代码（已弃用）
    MACRO_GRID_SPACING: float = 7.0      # DEPRECATED: Use H3_RES_LEVEL1 instead
    MACRO_SEARCH_RADIUS: int = 5000      # DEPRECATED: Use SEARCH_RADIUS_LEVEL1 instead
    FINE_GRID_SPACING: float = 1.4       # DEPRECATED: Use H3_RES_LEVEL2 instead
    FINE_SEARCH_RADIUS: int = 1000       # DEPRECATED: Use SEARCH_RADIUS_LEVEL2 instead
    RECURSION_SPACING_FACTOR: float = 0.5  # DEPRECATED: H3 uses fixed resolution levels
    RECURSION_RADIUS_FACTOR: float = 0.5   # DEPRECATED: H3 uses fixed resolution levels
    
    # Cost Control Parameters
    MAX_BUDGET: float = 200.0            # Maximum budget for API calls (in USD)
    API_COST_PER_CALL: float = 0.032     # Estimated cost per API call
    
    # Boundary Filtering Parameters
    ENABLE_BOUNDARY_FILTER: bool = True  # Enable/disable circular boundary filtering
    BOUNDARY_BUFFER_KM: float = 0.5      # Buffer distance in kilometers for boundary filtering
    
    # API Call Parameters
    API_TIMEOUT: int = 30                # API request timeout in seconds
    MAX_RETRIES: int = 3                 # Maximum retries for failed API calls
    RETRY_DELAY: int = 2                 # Initial retry delay in seconds (exponential backoff applied)

    # Search Area Parameters (example values, should be set by CLI/user)
    center_latitude: float = 34.0522     # Default center latitude (e.g., Los Angeles)
    center_longitude: float = -118.2437  # Default center longitude (e.g., Los Angeles)
    scan_radius_km: float = 50.0         # Total scan radius in kilometers

    # Google Places API Parameters
    PLACE_TYPES: List[str] = field(default_factory=lambda: [
        'convenience_store',
        'supermarket',
        'grocery_store',
        'gas_station'
    ])

    RESPONSE_FIELDS: List[str] = field(default_factory=lambda: [
        'places.id',
        'places.displayName',
        'places.types',
        'places.formattedAddress',
        'places.location',
        'places.photos'
    ])
    
    # Testing and development options
    skip_system_checks: bool = False
    mock_mode: bool = False              # Use mock data instead of real API calls for testing

    def __post_init__(self):
        """
        Performs comprehensive validation after initialization.
        """
        self._validate_parameters()
        if not self.skip_system_checks:
            self._check_system_requirements()

    def _validate_parameters(self):
        """
        Validates all configuration parameters for correctness and consistency.
        """
        errors = []
        
        # Validate coordinate bounds
        if not (-90 <= self.center_latitude <= 90):
            errors.append(f"中心纬度必须在-90到90之间，当前值: {self.center_latitude}")
        
        if not (-180 <= self.center_longitude <= 180):
            errors.append(f"中心经度必须在-180到180之间，当前值: {self.center_longitude}")
        
        # Validate grid spacing parameters
        if self.MACRO_GRID_SPACING <= 0:
            errors.append(f"宏网格间距必须大于0，当前值: {self.MACRO_GRID_SPACING}")
        elif self.MACRO_GRID_SPACING > 100:
            errors.append(f"宏网格间距过大 (>{100}km)，可能导致计算错误")
        
        if self.FINE_GRID_SPACING <= 0:
            errors.append(f"精网格间距必须大于0，当前值: {self.FINE_GRID_SPACING}")
        elif self.FINE_GRID_SPACING >= self.MACRO_GRID_SPACING:
            errors.append(f"精网格间距 ({self.FINE_GRID_SPACING}) 应小于宏网格间距 ({self.MACRO_GRID_SPACING})")
        
        # Validate search radius parameters
        if self.MACRO_SEARCH_RADIUS <= 0:
            errors.append(f"宏搜索半径必须大于0，当前值: {self.MACRO_SEARCH_RADIUS}")
        elif self.MACRO_SEARCH_RADIUS > 50000:  # Google Places API limit
            errors.append(f"宏搜索半径超过API限制 (50km)，当前值: {self.MACRO_SEARCH_RADIUS}m")
        
        if self.FINE_SEARCH_RADIUS <= 0:
            errors.append(f"精搜索半径必须大于0，当前值: {self.FINE_SEARCH_RADIUS}")
        elif self.FINE_SEARCH_RADIUS > self.MACRO_SEARCH_RADIUS:
            errors.append(f"精搜索半径 ({self.FINE_SEARCH_RADIUS}) 应小于宏搜索半径 ({self.MACRO_SEARCH_RADIUS})")
        
        if self.MIN_SEARCH_RADIUS <= 0:
            errors.append(f"最小搜索半径必须大于0，当前值: {self.MIN_SEARCH_RADIUS}")
        elif self.MIN_SEARCH_RADIUS >= self.FINE_SEARCH_RADIUS:
            errors.append(f"最小搜索半径 ({self.MIN_SEARCH_RADIUS}) 应小于精搜索半径 ({self.FINE_SEARCH_RADIUS})")
        
        # Validate scan area
        if self.scan_radius_km <= 0:
            errors.append(f"扫描半径必须大于0，当前值: {self.scan_radius_km}")
        elif self.scan_radius_km > 1000:
            errors.append(f"扫描半径过大 (>{1000}km)，可能导致过多网格点和高昂成本")
        
        # Validate recursion parameters
        if self.MAX_RECURSION_DEPTH < 0:
            errors.append(f"最大递归深度不能为负，当前值: {self.MAX_RECURSION_DEPTH}")
        elif self.MAX_RECURSION_DEPTH > 10:
            errors.append(f"最大递归深度过大 (>{10})，可能导致指数级网格点增长")
        
        if self.RECURSION_TRIGGER_COUNT <= 0:
            errors.append(f"递归触发阈值必须大于0，当前值: {self.RECURSION_TRIGGER_COUNT}")
        elif self.RECURSION_TRIGGER_COUNT > 100:
            errors.append(f"递归触发阈值过大 (>{100})，可能永远不会触发递归")
        
        # Validate scaling factors
        if not (0 < self.RECURSION_SPACING_FACTOR < 1):
            errors.append(f"递归间距因子必须在0到1之间，当前值: {self.RECURSION_SPACING_FACTOR}")
        
        if not (0 < self.RECURSION_RADIUS_FACTOR < 1):
            errors.append(f"递归半径因子必须在0到1之间，当前值: {self.RECURSION_RADIUS_FACTOR}")
        
        # Validate budget parameters
        if self.MAX_BUDGET <= 0:
            errors.append(f"最大预算必须大于0，当前值: {self.MAX_BUDGET}")
        elif self.MAX_BUDGET > 10000:
            errors.append(f"最大预算过大 (>${10000})，请确认是否正确")
        
        if self.API_COST_PER_CALL <= 0:
            errors.append(f"每次API调用成本必须大于0，当前值: {self.API_COST_PER_CALL}")
        elif self.API_COST_PER_CALL > 1:
            errors.append(f"每次API调用成本异常高 (>${1})，请检查价格设置")
        
        # Validate API parameters
        if self.API_TIMEOUT <= 0:
            errors.append(f"API超时时间必须大于0，当前值: {self.API_TIMEOUT}")
        elif self.API_TIMEOUT > 300:
            errors.append(f"API超时时间过长 (>{300}秒)，建议降低")
        
        if self.MAX_RETRIES < 0:
            errors.append(f"最大重试次数不能为负，当前值: {self.MAX_RETRIES}")
        elif self.MAX_RETRIES > 10:
            errors.append(f"最大重试次数过多 (>{10})，可能导致延迟过长")
        
        if self.RETRY_DELAY <= 0:
            errors.append(f"重试延迟必须大于0，当前值: {self.RETRY_DELAY}")
        
        # Validate place types and response fields
        if not self.PLACE_TYPES:
            errors.append("地点类型列表不能为空")
        
        if not self.RESPONSE_FIELDS:
            errors.append("响应字段列表不能为空")
        
        # Validate boundary filter parameters
        if self.BOUNDARY_BUFFER_KM < 0:
            errors.append(f"边界缓冲距离不能为负，当前值: {self.BOUNDARY_BUFFER_KM}")
        
        # Cross-validation: estimate potential grid points and cost
        estimated_grid_points = self._estimate_grid_points()
        estimated_cost = estimated_grid_points * self.API_COST_PER_CALL
        
        if estimated_cost > self.MAX_BUDGET * 2:
            errors.append(f"预估成本 (${estimated_cost:.2f}) 远超预算 (${self.MAX_BUDGET:.2f})，"
                         f"建议增大网格间距或减小扫描半径")
        
        if estimated_grid_points > 100000:
            errors.append(f"预估网格点数 ({estimated_grid_points}) 过多，可能导致扫描时间过长")
        
        if errors:
            error_message = "配置验证失败:\n" + "\n".join(f"- {error}" for error in errors)
            raise ValueError(error_message)

    def _estimate_grid_points(self) -> int:
        """
        Estimates the total number of grid points based on current configuration.
        """
        import math
        
        # Rough estimation based on area coverage
        scan_area = math.pi * (self.scan_radius_km ** 2)
        
        # Macro grid points
        macro_grid_area = self.MACRO_GRID_SPACING ** 2
        macro_points = max(1, int(scan_area / macro_grid_area))
        
        # Assume 10% of macro points become hotspots
        hotspot_factor = 0.1
        fine_scan_area = scan_area * hotspot_factor
        fine_grid_area = self.FINE_GRID_SPACING ** 2
        fine_points = max(0, int(fine_scan_area / fine_grid_area))
        
        # Assume 5% of fine points trigger enhanced scan
        enhanced_factor = 0.05
        enhanced_points = max(0, int(fine_points * enhanced_factor * 4))  # 4 points per enhanced grid
        
        return macro_points + fine_points + enhanced_points

    def _check_system_requirements(self):
        """
        Checks system requirements and available resources.
        """
        errors = []
        warnings = []
        
        # Check available disk space
        try:
            total, used, free = shutil.disk_usage(".")
            free_gb = free / (1024**3)
            
            if free_gb < 1:
                errors.append(f"可用磁盘空间不足 ({free_gb:.1f}GB)，至少需要1GB")
            elif free_gb < 5:
                warnings.append(f"可用磁盘空间较少 ({free_gb:.1f}GB)，建议至少5GB")
        except OSError:
            warnings.append("无法检查磁盘空间")
        
        # Check Python version
        import sys
        if sys.version_info < (3, 8):
            errors.append(f"Python版本过低 ({sys.version})，至少需要Python 3.8")
        
        # Check required environment variables (skip in mock mode)
        if not self.mock_mode and not os.getenv('GOOGLE_PLACES_API_KEY'):
            errors.append("未设置GOOGLE_PLACES_API_KEY环境变量")
        
        # Check memory availability (rough estimate)
        try:
            import psutil
            available_memory_gb = psutil.virtual_memory().available / (1024**3)
            
            if available_memory_gb < 0.5:
                errors.append(f"可用内存不足 ({available_memory_gb:.1f}GB)，至少需要0.5GB")
            elif available_memory_gb < 2:
                warnings.append(f"可用内存较少 ({available_memory_gb:.1f}GB)，建议至少2GB")
        except ImportError:
            warnings.append("无法检查内存状态 (psutil未安装)")
        
        if errors:
            error_message = "系统要求检查失败:\n" + "\n".join(f"- {error}" for error in errors)
            raise RuntimeError(error_message)
        
        if warnings:
            warning_message = "系统警告:\n" + "\n".join(f"- {warning}" for warning in warnings)
            print(f"⚠️ {warning_message}")

    def validate_for_session_resume(self, saved_config: Dict[str, Any]) -> bool:
        """
        Validates if current config is compatible with a saved session config.
        
        Args:
            saved_config: Previously saved configuration dictionary
            
        Returns:
            True if compatible, False otherwise
        """
        # Critical parameters that must match exactly
        critical_params = [
            'center_latitude', 'center_longitude', 'scan_radius_km',
            'PLACE_TYPES', 'RESPONSE_FIELDS'
        ]
        
        for param in critical_params:
            saved_value = saved_config.get(param)
            current_value = getattr(self, param, None)
            
            if param in ['PLACE_TYPES', 'RESPONSE_FIELDS']:
                # For lists, check if they contain the same elements
                if set(saved_value or []) != set(current_value or []):
                    print(f"❌ 关键参数不匹配: {param}")
                    return False
            else:
                # For numeric values, allow small floating point differences
                if isinstance(saved_value, (int, float)) and isinstance(current_value, (int, float)):
                    if abs(saved_value - current_value) > 1e-6:
                        print(f"❌ 关键参数不匹配: {param} (保存值: {saved_value}, 当前值: {current_value})")
                        return False
                elif saved_value != current_value:
                    print(f"❌ 关键参数不匹配: {param} (保存值: {saved_value}, 当前值: {current_value})")
                    return False
        
        return True

    def to_dict(self) -> Dict[str, Any]:
        """
        Converts the configuration to a dictionary with enhanced metadata.
        """
        config_dict = asdict(self)
        config_dict['_metadata'] = {
            'created_at': __import__('datetime').datetime.now().isoformat(),
            'estimated_grid_points': self._estimate_grid_points(),
            'estimated_cost': self._estimate_grid_points() * self.API_COST_PER_CALL,
            'version': '1.0'
        }
        return config_dict

    @classmethod  
    def from_dict(cls, data: Dict[str, Any]) -> 'ScanConfig':
        """
        Creates a ScanConfig instance from a dictionary.
        """
        # Remove metadata if present
        config_data = {k: v for k, v in data.items() if not k.startswith('_')}
        return cls(**config_data)

    def get_summary(self) -> str:
        """
        Returns a human-readable summary of the configuration.
        """
        estimated_points = self._estimate_grid_points()
        estimated_cost = estimated_points * self.API_COST_PER_CALL
        
        return f"""
📋 扫描配置摘要:
  🎯 目标区域: ({self.center_latitude:.4f}, {self.center_longitude:.4f}) 半径 {self.scan_radius_km}km
  🔧 网格配置: 宏 {self.MACRO_GRID_SPACING}km, 精 {self.FINE_GRID_SPACING}km
  🔍 搜索半径: 宏 {self.MACRO_SEARCH_RADIUS}m, 精 {self.FINE_SEARCH_RADIUS}m
  🔄 递归设置: 最大深度 {self.MAX_RECURSION_DEPTH}, 触发阈值 {self.RECURSION_TRIGGER_COUNT}
  💰 预算控制: 最大 ${self.MAX_BUDGET:.2f}, 每次调用 ${self.API_COST_PER_CALL:.3f}
  📊 预估统计: {estimated_points} 网格点, 约 ${estimated_cost:.2f} 成本
  🏪 地点类型: {', '.join(self.PLACE_TYPES)}
        """.strip()
