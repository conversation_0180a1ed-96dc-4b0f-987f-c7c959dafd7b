"""
H3 Visualizer - 可视化支持

提供H3网格的可视化支持功能，包括：
- 获取网格边界坐标
- 获取网格覆盖范围信息
- 支持地图可视化需求
"""

import h3
from typing import List, Tuple, Dict, Optional
from .h3_converter import H3Converter


class H3Visualizer:
    """H3可视化支持服务"""
    
    @staticmethod
    def get_grid_boundary(h3_id: str) -> List[Tuple[float, float]]:
        """
        获取网格边界坐标（用于地图绘制）
        
        Args:
            h3_id: H3网格ID
            
        Returns:
            边界坐标列表 [(lat, lng), ...]
        """
        if not H3Converter.validate_h3_id(h3_id):
            raise ValueError(f"Invalid H3 grid ID: {h3_id}")
        
        try:
            # 获取网格边界
            boundary = h3.cell_to_boundary(h3_id)
            # 转换为 (lat, lng) 格式
            return [(lat, lng) for lat, lng in boundary]
            
        except Exception as e:
            raise ValueError(f"Failed to get boundary for {h3_id}: {e}")
    
    @staticmethod
    def get_grid_coverage_info(h3_id: str) -> Dict:
        """
        获取网格覆盖范围信息
        
        Args:
            h3_id: H3网格ID
            
        Returns:
            覆盖范围信息字典
        """
        if not H3Converter.validate_h3_id(h3_id):
            raise ValueError(f"Invalid H3 grid ID: {h3_id}")
        
        try:
            # 获取基础信息
            grid_info = H3Converter.get_grid_info(h3_id)
            
            # 获取边界
            boundary = H3Visualizer.get_grid_boundary(h3_id)
            
            # 计算边界框
            lats = [point[0] for point in boundary]
            lngs = [point[1] for point in boundary]
            
            bbox = {
                "north": max(lats),
                "south": min(lats),
                "east": max(lngs),
                "west": min(lngs)
            }
            
            return {
                "h3_id": h3_id,
                "resolution": grid_info["resolution"],
                "center": {
                    "lat": grid_info["center_lat"],
                    "lng": grid_info["center_lng"]
                },
                "boundary": boundary,
                "bounding_box": bbox,
                "area_km2": grid_info["area_km2"],
                "edge_length_m": grid_info["edge_length_m"]
            }
            
        except Exception as e:
            raise ValueError(f"Failed to get coverage info for {h3_id}: {e}")
    
    @staticmethod
    def get_multiple_grids_coverage(h3_ids: List[str]) -> Dict:
        """
        获取多个网格的整体覆盖范围信息
        
        Args:
            h3_ids: H3网格ID列表
            
        Returns:
            整体覆盖范围信息字典
        """
        if not h3_ids:
            raise ValueError("h3_ids cannot be empty")
        
        # 验证所有网格ID
        invalid_ids = [h3_id for h3_id in h3_ids if not H3Converter.validate_h3_id(h3_id)]
        if invalid_ids:
            raise ValueError(f"Invalid H3 grid IDs: {invalid_ids}")
        
        try:
            all_boundaries = []
            grid_details = []
            total_area_km2 = 0
            
            for h3_id in h3_ids:
                coverage_info = H3Visualizer.get_grid_coverage_info(h3_id)
                all_boundaries.extend(coverage_info["boundary"])
                grid_details.append(coverage_info)
                total_area_km2 += coverage_info["area_km2"]
            
            # 计算整体边界框
            all_lats = [point[0] for point in all_boundaries]
            all_lngs = [point[1] for point in all_boundaries]
            
            overall_bbox = {
                "north": max(all_lats),
                "south": min(all_lats),
                "east": max(all_lngs),
                "west": min(all_lngs)
            }
            
            # 计算中心点
            center_lat = (overall_bbox["north"] + overall_bbox["south"]) / 2
            center_lng = (overall_bbox["east"] + overall_bbox["west"]) / 2
            
            return {
                "grid_count": len(h3_ids),
                "overall_center": {
                    "lat": center_lat,
                    "lng": center_lng
                },
                "overall_bounding_box": overall_bbox,
                "total_area_km2": total_area_km2,
                "grid_details": grid_details
            }
            
        except Exception as e:
            raise ValueError(f"Failed to get multiple grids coverage: {e}")
    
    @staticmethod
    def get_grid_geojson(h3_id: str) -> Dict:
        """
        获取网格的GeoJSON表示
        
        Args:
            h3_id: H3网格ID
            
        Returns:
            GeoJSON格式的网格数据
        """
        if not H3Converter.validate_h3_id(h3_id):
            raise ValueError(f"Invalid H3 grid ID: {h3_id}")
        
        try:
            boundary = H3Visualizer.get_grid_boundary(h3_id)
            grid_info = H3Converter.get_grid_info(h3_id)
            
            # 转换为GeoJSON格式（注意GeoJSON使用 [lng, lat] 顺序）
            coordinates = [[lng, lat] for lat, lng in boundary]
            # 闭合多边形
            coordinates.append(coordinates[0])
            
            geojson = {
                "type": "Feature",
                "properties": {
                    "h3_id": h3_id,
                    "resolution": grid_info["resolution"],
                    "center_lat": grid_info["center_lat"],
                    "center_lng": grid_info["center_lng"],
                    "area_km2": grid_info["area_km2"],
                    "edge_length_m": grid_info["edge_length_m"]
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [coordinates]
                }
            }
            
            return geojson
            
        except Exception as e:
            raise ValueError(f"Failed to get GeoJSON for {h3_id}: {e}")
    
    @staticmethod
    def get_multiple_grids_geojson(h3_ids: List[str]) -> Dict:
        """
        获取多个网格的GeoJSON FeatureCollection
        
        Args:
            h3_ids: H3网格ID列表
            
        Returns:
            GeoJSON FeatureCollection
        """
        if not h3_ids:
            raise ValueError("h3_ids cannot be empty")
        
        try:
            features = []
            for h3_id in h3_ids:
                feature = H3Visualizer.get_grid_geojson(h3_id)
                features.append(feature)
            
            return {
                "type": "FeatureCollection",
                "features": features
            }
            
        except Exception as e:
            raise ValueError(f"Failed to get GeoJSON for multiple grids: {e}")
    
    @staticmethod
    def calculate_optimal_zoom_level(h3_ids: List[str]) -> int:
        """
        根据网格列表计算最佳地图缩放级别
        
        Args:
            h3_ids: H3网格ID列表
            
        Returns:
            建议的地图缩放级别
        """
        if not h3_ids:
            return 10  # 默认缩放级别
        
        try:
            coverage_info = H3Visualizer.get_multiple_grids_coverage(h3_ids)
            bbox = coverage_info["overall_bounding_box"]
            
            # 计算边界框的大小
            lat_span = bbox["north"] - bbox["south"]
            lng_span = bbox["east"] - bbox["west"]
            max_span = max(lat_span, lng_span)
            
            # 根据跨度估算缩放级别
            if max_span > 10:
                return 6
            elif max_span > 5:
                return 7
            elif max_span > 2:
                return 8
            elif max_span > 1:
                return 9
            elif max_span > 0.5:
                return 10
            elif max_span > 0.1:
                return 11
            elif max_span > 0.05:
                return 12
            else:
                return 13
                
        except Exception:
            return 10  # 出错时返回默认值
