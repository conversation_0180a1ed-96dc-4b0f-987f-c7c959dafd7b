"""
H3 Converter - 基础坐标转换功能

提供H3网格系统的基础转换功能，包括：
- 区域转H3网格列表
- H3网格ID转中心坐标
- 生成子网格
- 获取网格信息
"""

import h3
import math
from typing import List, Tuple, Dict, Optional


class H3Converter:
    """H3基础转换服务"""
    
    @staticmethod
    def area_to_grids(center_lat: float, center_lng: float, radius_km: float, h3_resolution: int) -> List[str]:
        """
        将圆形区域转换为H3网格列表
        
        Args:
            center_lat: 中心纬度
            center_lng: 中心经度
            radius_km: 半径（公里）
            h3_resolution: H3分辨率
            
        Returns:
            H3网格ID列表
        """
        if not (-90 <= center_lat <= 90):
            raise ValueError(f"Invalid latitude: {center_lat}, must be between -90 and 90")
        if not (-180 <= center_lng <= 180):
            raise ValueError(f"Invalid longitude: {center_lng}, must be between -180 and 180")
        if radius_km <= 0:
            raise ValueError(f"Invalid radius: {radius_km}, must be positive")
        if not (0 <= h3_resolution <= 15):
            raise ValueError(f"Invalid H3 resolution: {h3_resolution}, must be between 0 and 15")
        
        try:
            # 将半径从公里转换为米
            radius_m = radius_km * 1000
            
            # 使用H3的grid_disk函数获取覆盖区域的网格
            center_h3 = h3.latlng_to_cell(center_lat, center_lng, h3_resolution)
            
            # 计算需要的环数（k）来覆盖指定半径
            # H3网格的平均边长
            avg_edge_length_m = h3.average_hexagon_edge_length(h3_resolution, unit='m')
            # 估算需要的环数
            k = max(1, int(math.ceil(radius_m / (avg_edge_length_m * 2))))
            
            # 获取网格
            hexes = h3.grid_disk(center_h3, k)
            
            # 过滤掉超出半径的网格
            filtered_hexes = []
            for hex_id in hexes:
                hex_lat, hex_lng = h3.cell_to_latlng(hex_id)
                distance_km = H3Converter._haversine_distance(center_lat, center_lng, hex_lat, hex_lng)
                if distance_km <= radius_km:
                    filtered_hexes.append(hex_id)
            
            return filtered_hexes
            
        except Exception as e:
            raise ValueError(f"Failed to convert area to grids: {e}")
    
    @staticmethod
    def grid_to_center(h3_id: str) -> Tuple[float, float]:
        """
        H3网格ID转中心坐标
        
        Args:
            h3_id: H3网格ID
            
        Returns:
            (纬度, 经度) 元组
        """
        if not h3_id or not isinstance(h3_id, str):
            raise ValueError("h3_id must be a non-empty string")
        
        try:
            lat, lng = h3.cell_to_latlng(h3_id)
            return lat, lng
        except Exception as e:
            raise ValueError(f"Invalid H3 grid ID: {h3_id}, error: {e}")
    
    @staticmethod
    def generate_children(parent_h3_id: str, child_resolution: int) -> List[str]:
        """
        生成子网格
        
        Args:
            parent_h3_id: 父网格ID
            child_resolution: 子网格分辨率
            
        Returns:
            子网格ID列表
        """
        if not parent_h3_id or not isinstance(parent_h3_id, str):
            raise ValueError("parent_h3_id must be a non-empty string")
        
        try:
            parent_resolution = h3.get_resolution(parent_h3_id)
            if child_resolution <= parent_resolution:
                raise ValueError(f"Child resolution ({child_resolution}) must be greater than parent resolution ({parent_resolution})")
            
            children = h3.cell_to_children(parent_h3_id, child_resolution)
            return list(children)
            
        except Exception as e:
            raise ValueError(f"Failed to generate children for {parent_h3_id}: {e}")
    
    @staticmethod
    def get_grid_info(h3_id: str) -> Dict:
        """
        获取网格信息
        
        Args:
            h3_id: H3网格ID
            
        Returns:
            网格信息字典
        """
        if not h3_id or not isinstance(h3_id, str):
            raise ValueError("h3_id must be a non-empty string")
        
        try:
            resolution = h3.get_resolution(h3_id)
            lat, lng = h3.cell_to_latlng(h3_id)
            area_km2 = h3.cell_area(h3_id, unit='km^2')
            edge_length_m = h3.average_hexagon_edge_length(resolution, unit='m')
            
            return {
                "h3_id": h3_id,
                "resolution": resolution,
                "center_lat": lat,
                "center_lng": lng,
                "area_km2": area_km2,
                "edge_length_m": edge_length_m,
                "is_valid": h3.is_valid_cell(h3_id)
            }
            
        except Exception as e:
            raise ValueError(f"Failed to get grid info for {h3_id}: {e}")
    
    @staticmethod
    def _haversine_distance(lat1: float, lng1: float, lat2: float, lng2: float) -> float:
        """
        计算两点间的球面距离（公里）
        
        Args:
            lat1, lng1: 第一个点的纬度和经度
            lat2, lng2: 第二个点的纬度和经度
            
        Returns:
            距离（公里）
        """
        # 地球半径（公里）
        R = 6371.0
        
        # 转换为弧度
        lat1_rad = math.radians(lat1)
        lng1_rad = math.radians(lng1)
        lat2_rad = math.radians(lat2)
        lng2_rad = math.radians(lng2)
        
        # 计算差值
        dlat = lat2_rad - lat1_rad
        dlng = lng2_rad - lng1_rad
        
        # Haversine公式
        a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlng/2)**2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        
        return R * c
    
    @staticmethod
    def validate_h3_id(h3_id: str) -> bool:
        """
        验证H3网格ID是否有效
        
        Args:
            h3_id: H3网格ID
            
        Returns:
            是否有效
        """
        if not h3_id or not isinstance(h3_id, str):
            return False
        
        try:
            return h3.is_valid_cell(h3_id)
        except:
            return False
    
    @staticmethod
    def get_resolution_info(resolution: int) -> Dict:
        """
        获取H3分辨率信息
        
        Args:
            resolution: H3分辨率 (0-15)
            
        Returns:
            分辨率信息字典
        """
        if not (0 <= resolution <= 15):
            raise ValueError(f"Invalid resolution: {resolution}, must be between 0 and 15")
        
        try:
            avg_area_km2 = h3.average_hexagon_area(resolution, unit='km^2')
            avg_edge_length_m = h3.average_hexagon_edge_length(resolution, unit='m')
            
            return {
                "resolution": resolution,
                "average_area_km2": avg_area_km2,
                "average_edge_length_m": avg_edge_length_m,
                "total_cells": h3.get_num_cells(resolution)
            }
            
        except Exception as e:
            raise ValueError(f"Failed to get resolution info for {resolution}: {e}")
