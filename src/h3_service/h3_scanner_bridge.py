"""
H3 Scanner Bridge - 扫描桥接

连接项目管理层和核心扫描引擎，提供：
- 配置格式转换
- 单网格扫描包装
- 结果格式转换
- 批量网格扫描
"""

import os
import sys
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
from datetime import datetime

# 添加 core_code 到路径
core_code_path = os.path.join(os.path.dirname(__file__), '..', '..', 'core_code')
sys.path.insert(0, core_code_path)

from adaptive_grid_scan import AdaptiveScanner
from .h3_converter import H3Converter


@dataclass
class ScanConfig:
    """扫描配置"""
    place_types: List[str]
    layer_config: List[Dict[str, Any]]  # [{"h3_res": 5, "search_radius": 5000}, ...]
    mock_mode: bool = True
    max_retries: int = 3
    timeout_seconds: int = 30


@dataclass
class GridSpec:
    """网格扫描规格"""
    grid_id: str
    level: int
    place_types: Optional[List[str]] = None  # 如果为None，使用配置中的默认值


@dataclass
class GridScanResult:
    """网格扫描结果"""
    grid_id: str
    level: int
    places: List[Dict]
    should_drill_down: bool
    metadata: Dict[str, Any]
    success: bool = True
    error_message: Optional[str] = None


class H3ScannerBridge:
    """H3扫描桥接服务"""
    
    def __init__(self, project_config: ScanConfig):
        """
        使用项目级配置初始化
        
        Args:
            project_config: 项目扫描配置
        """
        self.config = project_config
        self.scanner = AdaptiveScanner(mock=project_config.mock_mode)
        
        # 验证配置
        self._validate_config()
    
    def _validate_config(self):
        """验证配置有效性"""
        if not self.config.place_types:
            raise ValueError("place_types cannot be empty")
        
        if not self.config.layer_config:
            raise ValueError("layer_config cannot be empty")
        
        for i, layer in enumerate(self.config.layer_config):
            if "h3_res" not in layer or "search_radius" not in layer:
                raise ValueError(f"Layer {i} missing required fields: h3_res, search_radius")
            
            if not isinstance(layer["h3_res"], int) or layer["h3_res"] < 0 or layer["h3_res"] > 15:
                raise ValueError(f"Layer {i} invalid h3_res: {layer['h3_res']}")
            
            if not isinstance(layer["search_radius"], (int, float)) or layer["search_radius"] <= 0:
                raise ValueError(f"Layer {i} invalid search_radius: {layer['search_radius']}")
    
    def scan_single_grid(self, grid_id: str, level: int, place_types: Optional[List[str]] = None) -> GridScanResult:
        """
        单网格扫描
        
        Args:
            grid_id: H3网格ID
            level: 扫描层级 (1, 2, 3, ...)
            place_types: 地点类型列表，如果为None则使用配置中的默认值
            
        Returns:
            网格扫描结果
        """
        # 使用提供的place_types或配置中的默认值
        scan_place_types = place_types or self.config.place_types
        
        try:
            # 验证输入
            if not H3Converter.validate_h3_id(grid_id):
                raise ValueError(f"Invalid H3 grid ID: {grid_id}")
            
            if level < 1 or level > len(self.config.layer_config):
                raise ValueError(f"Invalid level: {level}, must be between 1 and {len(self.config.layer_config)}")
            
            # 调用核心扫描引擎
            result = self.scanner.run_single_grid(
                grid_id=grid_id,
                level=level,
                place_types=scan_place_types,
                layer_config=self.config.layer_config
            )
            
            # 转换结果格式
            return GridScanResult(
                grid_id=grid_id,
                level=level,
                places=result["places"],
                should_drill_down=result["should_drill_down"],
                metadata=result["metadata"],
                success=True
            )
            
        except Exception as e:
            return GridScanResult(
                grid_id=grid_id,
                level=level,
                places=[],
                should_drill_down=False,
                metadata={
                    "error": str(e),
                    "scan_time": datetime.now().isoformat()
                },
                success=False,
                error_message=str(e)
            )
    
    def scan_multiple_grids(self, grid_specs: List[GridSpec]) -> List[GridScanResult]:
        """
        批量网格扫描
        
        Args:
            grid_specs: 网格扫描规格列表
            
        Returns:
            网格扫描结果列表
        """
        if not grid_specs:
            return []
        
        results = []
        for spec in grid_specs:
            result = self.scan_single_grid(
                grid_id=spec.grid_id,
                level=spec.level,
                place_types=spec.place_types
            )
            results.append(result)
        
        return results
    
    def get_scanner_stats(self) -> Dict[str, Any]:
        """
        获取扫描器统计信息
        
        Returns:
            统计信息字典
        """
        return {
            "api_calls_made": self.scanner.api_calls,
            "unique_places_found": len(self.scanner.visited_place_ids),
            "mock_mode": self.config.mock_mode,
            "config": {
                "place_types": self.config.place_types,
                "layer_count": len(self.config.layer_config),
                "max_retries": self.config.max_retries,
                "timeout_seconds": self.config.timeout_seconds
            }
        }
    
    def reset_scanner_state(self):
        """重置扫描器状态"""
        self.scanner.visited_place_ids.clear()
        self.scanner.api_calls = 0
        self.scanner.layer_data.clear()
    
    @staticmethod
    def create_default_config(place_types: List[str], mock_mode: bool = True) -> ScanConfig:
        """
        创建默认扫描配置
        
        Args:
            place_types: 地点类型列表
            mock_mode: 是否使用mock模式
            
        Returns:
            默认扫描配置
        """
        default_layer_config = [
            {"h3_res": 5, "search_radius": 5000},   # Level 1: 粗筛
            {"h3_res": 7, "search_radius": 1000},   # Level 2: 精筛
            {"h3_res": 8, "search_radius": 500},    # Level 3: 增强
        ]
        
        return ScanConfig(
            place_types=place_types,
            layer_config=default_layer_config,
            mock_mode=mock_mode,
            max_retries=3,
            timeout_seconds=30
        )
    
    def validate_grid_for_level(self, grid_id: str, level: int) -> bool:
        """
        验证网格是否适合指定层级
        
        Args:
            grid_id: H3网格ID
            level: 扫描层级
            
        Returns:
            是否适合
        """
        try:
            if not H3Converter.validate_h3_id(grid_id):
                return False
            
            if level < 1 or level > len(self.config.layer_config):
                return False
            
            # 检查网格分辨率是否与层级配置匹配
            grid_resolution = H3Converter.get_grid_info(grid_id)["resolution"]
            expected_resolution = self.config.layer_config[level - 1]["h3_res"]
            
            return grid_resolution == expected_resolution
            
        except Exception:
            return False
