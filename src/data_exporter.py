#!/usr/bin/env python3
"""
数据导出器 - 将执行结果整合并导出为多种格式

功能:
- 收集所有grid的执行结果
- 去重和数据清理
- 导出为JSON、CSV、Excel格式
- 生成统计报告
"""

import json
import csv
import os
from pathlib import Path
from typing import Dict, List, Any, Set, Tuple
from datetime import datetime
import pandas as pd


class DataExporter:
    """数据导出器"""
    
    def __init__(self, project_name: str):
        self.project_name = project_name
        self.project_dir = Path("projects") / project_name
        self.results_dir = self.project_dir / "results"
        self.exports_dir = self.project_dir / "exports"
        
        # 确保exports目录存在
        self.exports_dir.mkdir(exist_ok=True)
    
    def export_all_formats(self, deduplicate: bool = True) -> Dict[str, str]:
        """
        导出所有格式的数据
        
        Args:
            deduplicate: 是否去重（基于place_id）
            
        Returns:
            导出文件路径字典
        """
        print(f"🚀 开始导出项目 '{self.project_name}' 的数据...")
        
        # 收集所有数据
        all_places, stats = self._collect_all_results()
        
        if not all_places:
            print("❌ 没有找到任何执行结果")
            return {}
        
        print(f"📊 收集到 {len(all_places)} 个地点数据")
        
        # 去重处理
        if deduplicate:
            unique_places = self._deduplicate_places(all_places)
            print(f"🔄 去重后剩余 {len(unique_places)} 个唯一地点")
        else:
            unique_places = all_places
        
        # 生成时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 导出各种格式
        export_files = {}
        
        # 1. 导出完整JSON
        json_file = self._export_json(unique_places, stats, timestamp)
        export_files['json'] = json_file
        
        # 2. 导出CSV
        csv_file = self._export_csv(unique_places, timestamp)
        export_files['csv'] = csv_file
        
        # 3. 导出Excel (如果pandas可用)
        try:
            excel_file = self._export_excel(unique_places, stats, timestamp)
            export_files['excel'] = excel_file
        except ImportError:
            print("⚠️ pandas未安装，跳过Excel导出")
        
        # 4. 生成统计报告
        report_file = self._generate_report(stats, len(unique_places), timestamp)
        export_files['report'] = report_file
        
        print(f"✅ 数据导出完成！文件保存在: {self.exports_dir}")
        return export_files
    
    def _collect_all_results(self) -> Tuple[List[Dict], Dict]:
        """收集所有执行结果"""
        all_places = []
        stats = {
            'total_grids': 0,
            'successful_grids': 0,
            'total_places': 0,
            'by_type': {},
            'by_grid_level': {},
            'scan_time_range': {'earliest': None, 'latest': None}
        }
        
        if not self.results_dir.exists():
            return all_places, stats
        
        # 遍历所有结果文件
        for result_file in self.results_dir.glob("*_results.json"):
            try:
                with open(result_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                grid_id = data.get('grid_id')
                scan_time = data.get('scan_time')
                places = data.get('places', [])
                
                stats['total_grids'] += 1
                if places:
                    stats['successful_grids'] += 1
                    stats['total_places'] += len(places)
                
                # 统计时间范围
                if scan_time:
                    if not stats['scan_time_range']['earliest'] or scan_time < stats['scan_time_range']['earliest']:
                        stats['scan_time_range']['earliest'] = scan_time
                    if not stats['scan_time_range']['latest'] or scan_time > stats['scan_time_range']['latest']:
                        stats['scan_time_range']['latest'] = scan_time
                
                # 处理每个地点
                for place in places:
                    # 添加网格信息
                    place['source_grid_id'] = grid_id
                    place['source_file'] = result_file.name
                    
                    # 统计类型
                    for place_type in place.get('types', []):
                        stats['by_type'][place_type] = stats['by_type'].get(place_type, 0) + 1
                    
                    # 统计网格级别
                    level = place.get('scan_level', 1)
                    stats['by_grid_level'][f'level_{level}'] = stats['by_grid_level'].get(f'level_{level}', 0) + 1
                    
                    all_places.append(place)
                
            except Exception as e:
                print(f"⚠️ 读取结果文件失败 {result_file}: {e}")
                continue
        
        return all_places, stats
    
    def _deduplicate_places(self, places: List[Dict]) -> List[Dict]:
        """基于place_id去重"""
        seen_ids: Set[str] = set()
        unique_places = []
        
        for place in places:
            place_id = place.get('place_id')
            if place_id and place_id not in seen_ids:
                seen_ids.add(place_id)
                unique_places.append(place)
            elif not place_id:
                # 如果没有place_id，基于坐标去重
                coord_key = f"{place.get('latitude', 0):.6f},{place.get('longitude', 0):.6f}"
                if coord_key not in seen_ids:
                    seen_ids.add(coord_key)
                    unique_places.append(place)
        
        return unique_places
    
    def _export_json(self, places: List[Dict], stats: Dict, timestamp: str) -> str:
        """导出JSON格式"""
        output_file = self.exports_dir / f"places_export_{timestamp}.json"
        
        export_data = {
            'export_info': {
                'project_name': self.project_name,
                'export_time': datetime.now().isoformat(),
                'total_places': len(places),
                'data_source': 'grid_map_scanner'
            },
            'statistics': stats,
            'places': places
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        print(f"📄 JSON导出完成: {output_file.name}")
        return str(output_file)
    
    def _export_csv(self, places: List[Dict], timestamp: str) -> str:
        """导出CSV格式"""
        output_file = self.exports_dir / f"places_export_{timestamp}.csv"
        
        if not places:
            return str(output_file)
        
        # 获取所有字段名
        fieldnames = set()
        for place in places:
            fieldnames.update(place.keys())
        
        # 排序字段名，重要字段放前面
        important_fields = ['place_id', 'name', 'formatted_address', 'latitude', 'longitude', 'types']
        sorted_fields = important_fields + [f for f in sorted(fieldnames) if f not in important_fields]
        
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=sorted_fields)
            writer.writeheader()
            
            for place in places:
                # 处理列表字段（如types）
                row = {}
                for field in sorted_fields:
                    value = place.get(field, '')
                    if isinstance(value, list):
                        row[field] = '; '.join(str(v) for v in value)
                    else:
                        row[field] = value
                writer.writerow(row)
        
        print(f"📊 CSV导出完成: {output_file.name}")
        return str(output_file)
    
    def _export_excel(self, places: List[Dict], stats: Dict, timestamp: str) -> str:
        """导出Excel格式"""
        output_file = self.exports_dir / f"places_export_{timestamp}.xlsx"
        
        # 准备数据
        df_places = pd.DataFrame(places)
        
        # 处理types字段
        if 'types' in df_places.columns:
            df_places['types'] = df_places['types'].apply(
                lambda x: '; '.join(x) if isinstance(x, list) else str(x)
            )
        
        # 创建统计数据DataFrame
        stats_data = []
        for key, value in stats.items():
            if isinstance(value, dict):
                for sub_key, sub_value in value.items():
                    stats_data.append({'指标': f"{key}.{sub_key}", '数值': sub_value})
            else:
                stats_data.append({'指标': key, '数值': value})
        
        df_stats = pd.DataFrame(stats_data)
        
        # 写入Excel
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df_places.to_excel(writer, sheet_name='地点数据', index=False)
            df_stats.to_excel(writer, sheet_name='统计信息', index=False)
        
        print(f"📈 Excel导出完成: {output_file.name}")
        return str(output_file)
    
    def _generate_report(self, stats: Dict, unique_places_count: int, timestamp: str) -> str:
        """生成统计报告"""
        output_file = self.exports_dir / f"export_report_{timestamp}.txt"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"📊 项目 '{self.project_name}' 数据导出报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"项目名称: {self.project_name}\n\n")
            
            f.write("📈 执行统计:\n")
            f.write(f"  总网格数: {stats['total_grids']}\n")
            f.write(f"  成功网格: {stats['successful_grids']}\n")
            f.write(f"  成功率: {stats['successful_grids']/stats['total_grids']*100:.1f}%\n\n")
            
            f.write("📍 地点统计:\n")
            f.write(f"  原始地点数: {stats['total_places']}\n")
            f.write(f"  去重后地点数: {unique_places_count}\n")
            f.write(f"  去重率: {(1-unique_places_count/stats['total_places'])*100:.1f}%\n\n")
            
            if stats['by_type']:
                f.write("🏪 按类型统计:\n")
                for place_type, count in sorted(stats['by_type'].items(), key=lambda x: x[1], reverse=True):
                    f.write(f"  {place_type}: {count}\n")
                f.write("\n")
            
            if stats['by_grid_level']:
                f.write("🎯 按网格级别统计:\n")
                for level, count in stats['by_grid_level'].items():
                    f.write(f"  {level}: {count}\n")
                f.write("\n")
            
            if stats['scan_time_range']['earliest'] and stats['scan_time_range']['latest']:
                f.write("⏰ 扫描时间范围:\n")
                f.write(f"  开始时间: {stats['scan_time_range']['earliest']}\n")
                f.write(f"  结束时间: {stats['scan_time_range']['latest']}\n")
        
        print(f"📋 报告生成完成: {output_file.name}")
        return str(output_file)


def main():
    """命令行入口"""
    import sys
    
    if len(sys.argv) < 2:
        print("使用方法: python data_exporter.py <项目名称>")
        return
    
    project_name = sys.argv[1]
    exporter = DataExporter(project_name)
    
    try:
        export_files = exporter.export_all_formats()
        
        print("\n🎉 导出完成！生成的文件:")
        for format_type, file_path in export_files.items():
            print(f"  {format_type}: {Path(file_path).name}")
        
    except Exception as e:
        print(f"❌ 导出失败: {e}")


if __name__ == "__main__":
    main() 