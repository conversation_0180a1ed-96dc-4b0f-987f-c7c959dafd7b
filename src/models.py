from dataclasses import dataclass, field
from typing import List, Set, Optional, Dict
from datetime import datetime

class BudgetExceededException(Exception):
    """自定义异常：当API调用成本超出预算限制时抛出"""
    def __init__(self, current_cost: float, max_budget: float):
        self.current_cost = current_cost
        self.max_budget = max_budget
        super().__init__(f"预算超出：当前成本 ${current_cost:.2f} 超过最大预算 ${max_budget:.2f}")

@dataclass
class Coordinate:
    latitude: float
    longitude: float

    def __str__(self):
        return f"{self.latitude},{self.longitude}"

@dataclass
class GridPoint:
    center: Coordinate
    radius: int  # Search radius in meters
    level: int   # Scan level (1=macro, 2=fine, 3+=enhanced)
    id: str = field(init=False)

    def __post_init__(self):
        self._update_id()
    
    def _update_id(self):
        """Update the ID based on current level and coordinates"""
        self.id = f"grid_{self.level}_{self.center.latitude}_{self.center.longitude}"
    
    def __setattr__(self, name, value):
        super().__setattr__(name, value)
        # Regenerate ID when level changes
        if name == 'level' and hasattr(self, 'center'):
            self._update_id()

@dataclass
class Area:
    center: Coordinate
    radius_km: float
    name: str = ""

@dataclass
class PlaceData:
    place_id: str
    name: str
    formatted_address: str
    latitude: float
    longitude: float
    postal_address: str
    types: List[str]
    photos: List[str]
    # Scan metadata
    grid_point_id: str
    scan_time: str
    scan_level: int

    def to_csv_row(self) -> dict:
        return {
            'place_id': self.place_id,
            'name': self.name,
            'formatted_address': self.formatted_address,
            'latitude': self.latitude,
            'longitude': self.longitude,
            'postal_address': self.postal_address,
            'types': '|'.join(self.types),
            'photos': '|'.join(self.photos),
            'grid_point_id': self.grid_point_id,
            'scan_time': self.scan_time,
            'scan_level': self.scan_level
        }

@dataclass
class ScanSession:
    session_id: str
    target_area: Area
    config_snapshot: Dict
    created_time: str = field(default_factory=lambda: datetime.now().isoformat())
    last_updated: str = field(default_factory=lambda: datetime.now().isoformat())
    current_phase: str = "created"
    completed_grid_points: Set[str] = field(default_factory=set)
    hotspot_areas: List[Area] = field(default_factory=list)
    extreme_density_points: List[GridPoint] = field(default_factory=list)
    total_api_calls: int = 0
    total_places_found: int = 0
    current_cost: float = 0.0
    is_completed: bool = False
    
    # 新增：详细的网格层级记录
    grid_hierarchy: Dict = field(default_factory=lambda: {
        "level_1_macro": {
            "total_grids": 0,
            "completed_grids": 0,
            "pending_grids": 0,
            "grids": {}  # grid_id -> grid_info
        },
        "level_2_fine": {
            "total_grids": 0,
            "completed_grids": 0, 
            "pending_grids": 0,
            "grids": {}
        },
        "level_3_enhanced": {
            "total_grids": 0,
            "completed_grids": 0,
            "pending_grids": 0,
            "grids": {}
        }
    })
    
    # 当前处理状态
    current_processing: Dict = field(default_factory=lambda: {
        "level": 1,
        "grid_id": "",
        "phase_progress": "",
        "overall_progress": ""
    })

    def to_dict(self) -> dict:
        # Convert dataclasses and sets to JSON-serializable formats
        return {
            'session_id': self.session_id,
            'target_area': {
                'center': {'latitude': self.target_area.center.latitude, 'longitude': self.target_area.center.longitude},
                'radius_km': self.target_area.radius_km,
                'name': self.target_area.name
            },
            'config_snapshot': self.config_snapshot,
            'created_time': self.created_time,
            'last_updated': self.last_updated,
            'current_phase': self.current_phase,
            'completed_grid_points': list(self.completed_grid_points),
            'hotspot_areas': [{'center': {'latitude': area.center.latitude, 'longitude': area.center.longitude}, 'radius_km': area.radius_km, 'name': area.name} for area in self.hotspot_areas],
            'extreme_density_points': [{'id': point.id, 'center': {'latitude': point.center.latitude, 'longitude': point.center.longitude}, 'radius': point.radius, 'level': point.level} for point in self.extreme_density_points],
            'total_api_calls': self.total_api_calls,
            'total_places_found': self.total_places_found,
            'current_cost': self.current_cost,
            'is_completed': self.is_completed,
            'grid_hierarchy': self.grid_hierarchy,
            'current_processing': self.current_processing
        }

    @classmethod
    def from_dict(cls, data: dict) -> 'ScanSession':
        target_area_data = data['target_area']
        target_area = Area(
            center=Coordinate(**target_area_data['center']),
            radius_km=target_area_data['radius_km'],
            name=target_area_data.get('name', '')
        )

        hotspot_areas = [Area(center=Coordinate(**area['center']), radius_km=area['radius_km'], name=area.get('name', '')) for area in data.get('hotspot_areas', [])]
        extreme_density_points = [GridPoint(center=Coordinate(**point['center']), radius=point['radius'], level=point['level']) for point in data.get('extreme_density_points', [])]

        # Handle backward compatibility for time fields
        created_time = data.get('created_time', data.get('start_time', datetime.now().isoformat()))
        last_updated = data.get('last_updated', created_time)
        
        # Handle backward compatibility for completion status
        is_completed = data.get('is_completed', False)
        if 'status' in data:
            is_completed = data['status'] in ['completed', 'failed']

        # 处理新字段的默认值
        grid_hierarchy = data.get('grid_hierarchy', {
            "level_1_macro": {"total_grids": 0, "completed_grids": 0, "pending_grids": 0, "grids": {}},
            "level_2_fine": {"total_grids": 0, "completed_grids": 0, "pending_grids": 0, "grids": {}},
            "level_3_enhanced": {"total_grids": 0, "completed_grids": 0, "pending_grids": 0, "grids": {}}
        })
        
        current_processing = data.get('current_processing', {
            "level": 1, "grid_id": "", "phase_progress": "", "overall_progress": ""
        })

        return cls(
            session_id=data['session_id'],
            target_area=target_area,
            config_snapshot=data.get('config_snapshot', {}),
            created_time=created_time,
            last_updated=last_updated,
            current_phase=data.get('current_phase', 'created'),
            completed_grid_points=set(data.get('completed_grid_points', [])),
            hotspot_areas=hotspot_areas,
            extreme_density_points=extreme_density_points,
            total_api_calls=data.get('total_api_calls', 0),
            total_places_found=data.get('total_places_found', 0),
            current_cost=data.get('current_cost', 0.0),
            is_completed=is_completed,
            grid_hierarchy=grid_hierarchy,
            current_processing=current_processing
        )

    def add_grid_to_level(self, level: int, grid_id: str, parent_grid: str = None):
        """添加网格到指定层级"""
        level_key = f"level_{level}_{'macro' if level == 1 else 'fine' if level == 2 else 'enhanced'}"
        
        if level_key not in self.grid_hierarchy:
            return
            
        grid_info = {
            "grid_id": grid_id,
            "status": "pending",
            "places_found": 0,
            "parent_grid": parent_grid,
            "child_grids": [],
            "triggered_next_level": False,
            "recursion_depth": level - 2 if level > 2 else 0
        }
        
        self.grid_hierarchy[level_key]["grids"][grid_id] = grid_info
        self.grid_hierarchy[level_key]["total_grids"] += 1
        self.grid_hierarchy[level_key]["pending_grids"] += 1
        
        # 如果有父网格，建立父子关系
        if parent_grid and level > 1:
            parent_level_key = f"level_{level-1}_{'macro' if level-1 == 1 else 'fine' if level-1 == 2 else 'enhanced'}"
            if parent_level_key in self.grid_hierarchy and parent_grid in self.grid_hierarchy[parent_level_key]["grids"]:
                self.grid_hierarchy[parent_level_key]["grids"][parent_grid]["child_grids"].append(grid_id)
    
    def mark_grid_completed(self, level: int, grid_id: str, places_found: int, triggered_next: bool = False):
        """标记网格完成"""
        level_key = f"level_{level}_{'macro' if level == 1 else 'fine' if level == 2 else 'enhanced'}"
        
        if level_key in self.grid_hierarchy and grid_id in self.grid_hierarchy[level_key]["grids"]:
            grid_info = self.grid_hierarchy[level_key]["grids"][grid_id]
            grid_info["status"] = "completed"
            grid_info["places_found"] = places_found
            grid_info["triggered_next_level"] = triggered_next
            
            # 更新计数
            self.grid_hierarchy[level_key]["completed_grids"] += 1
            self.grid_hierarchy[level_key]["pending_grids"] -= 1
    
    def update_current_processing(self, level: int, grid_id: str, phase_progress: str):
        """更新当前处理状态"""
        self.current_processing = {
            "level": level,
            "grid_id": grid_id,
            "phase_progress": phase_progress,
            "overall_progress": self._calculate_overall_progress()
        }
    
    def _calculate_overall_progress(self) -> str:
        """计算总体进度"""
        total_completed = 0
        total_grids = 0
        
        for level_info in self.grid_hierarchy.values():
            total_completed += level_info["completed_grids"]
            total_grids += level_info["total_grids"]
        
        if total_grids == 0:
            return "0/0 (0%)"
        
        percentage = (total_completed / total_grids) * 100
        return f"{total_completed}/{total_grids} ({percentage:.1f}%)"
    
    def get_grid_summary(self) -> Dict:
        """获取网格摘要信息"""
        summary = {}
        for level, info in self.grid_hierarchy.items():
            level_name = level.replace("_", " ").title()
            summary[level_name] = {
                "total": info["total_grids"],
                "completed": info["completed_grids"],
                "pending": info["pending_grids"],
                "completion_rate": f"{(info['completed_grids'] / max(info['total_grids'], 1) * 100):.1f}%"
            }
        return summary


@dataclass
class ScanResult:
    total_places_found: int
    total_api_calls: int
    total_cost: float
    failed_grid_points: int
    scan_duration: str
    session_id: str

    def to_dict(self) -> dict:
        return {
            'total_places_found': self.total_places_found,
            'total_api_calls': self.total_api_calls,
            'total_cost': self.total_cost,
            'failed_grid_points': self.failed_grid_points,
            'scan_duration': self.scan_duration,
            'session_id': self.session_id,
            'cost_per_place': self.total_cost / max(self.total_places_found, 1)
        }
