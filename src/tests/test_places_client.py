import unittest
from unittest.mock import patch, MagicMock
import os
import requests

from src.config import ScanConfig
from src.places_client import PlacesAPIClient
from src.models import GridPoint, Coordinate, PlaceData

# Set a dummy API key for testing purposes
@patch.dict(os.environ, {"GOOGLE_PLACES_API_KEY": "test_api_key"})
class TestPlacesAPIClient(unittest.TestCase):

    def setUp(self):
        """Set up a common config and client for tests."""
        self.config = ScanConfig()
        # Patch the environment variable before initializing the client
        self.env_patcher = patch.dict(os.environ, {"GOOGLE_PLACES_API_KEY": "test_api_key"})
        self.env_patcher.start()
        self.addCleanup(self.env_patcher.stop)
        self.client = PlacesAPIClient(self.config)

    def test_initialization_with_api_key(self):
        """Test that the client initializes correctly with an API key."""
        self.assertEqual(self.client.api_key, "test_api_key")

    def test_initialization_without_api_key(self):
        """Test that the client raises an error if the API key is missing."""
        with patch.dict(os.environ, {}, clear=True):
            with self.assertRaises(ValueError):
                PlacesAPIClient(self.config)

    @patch('requests.post')
    def test_make_api_request(self, mock_post):
        """Test the API request method."""
        # Configure the mock to return a successful response
        mock_response = unittest.mock.Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"places": []}
        mock_post.return_value = mock_response

        client = PlacesAPIClient(self.config)
        
        # 修复：手动设置API密钥已验证，避免额外的验证调用
        client._api_key_validated = True
        
        center = Coordinate(latitude=34.0, longitude=-118.0)
        response = client._make_api_request(center, 5000)

        self.assertIsNotNone(response)
        self.assertEqual(response.status_code, 200)
        mock_post.assert_called_once()  # 修复：现在应该只调用一次

    def test_extract_places(self):
        """Test the extraction of place data from a mock API response."""
        client = PlacesAPIClient(self.config)
        mock_response_data = {
            "places": [
                {
                    "id": "test_id_1",
                    "name": "Test Store",
                    "formattedAddress": "123 Test St, Test City",
                    "location": {"latitude": 34.0, "longitude": -118.0},
                    "postalAddress": {
                        "addressLines": ["123 Test St"],
                        "postalCode": "12345",
                        "locality": "Test City"
                    },
                    "types": ["convenience_store"],
                    "photos": [{"name": "photo1"}]
                }
            ]
        }
        
        extracted = client._extract_places(mock_response_data, "grid_1", 1)
        self.assertEqual(len(extracted), 1)
        self.assertIsInstance(extracted[0], PlaceData)
        self.assertEqual(extracted[0].place_id, "test_id_1")
        self.assertEqual(extracted[0].scan_level, 1)

    @patch('src.places_client.requests.post')
    @patch('src.places_client.time.sleep', return_value=None)
    def test_nearby_search_retry_logic(self, mock_sleep, mock_post):
        """Test that nearby_search retries on failure."""
        # Simulate a sequence of failed responses followed by a success
        mock_post.side_effect = [
            requests.exceptions.RequestException("Test network error"),
            requests.exceptions.RequestException("Test network error"),
            unittest.mock.Mock(status_code=200, json=lambda: {"places": []})
        ]

        client = PlacesAPIClient(self.config)
        
        # 修复：跳过API密钥验证和速率限制检查
        client._api_key_validated = True
        client._last_request_time = 0  # 避免速率限制睡眠
        
        # The actual grid point data doesn't matter much for this test
        grid_point = GridPoint(center=Coordinate(0, 0), radius=100, level=1)
        
        result = client.nearby_search(grid_point)

        # It should have called post 3 times (2 fails, 1 success)
        self.assertEqual(mock_post.call_count, 3)
        # 修复：由于新的重试逻辑，可能有更多的睡眠调用(包括速率限制)
        self.assertGreaterEqual(mock_sleep.call_count, 2)  # 至少2次重试睡眠
        # It should eventually succeed
        self.assertIsNotNone(result)

    @patch('src.places_client.requests.post')
    @patch('src.places_client.time.sleep', return_value=None)
    def test_nearby_search_all_retries_fail(self, mock_sleep, mock_post):
        """Test that nearby_search returns None after all retries fail."""
        # Simulate only failed responses
        mock_post.side_effect = requests.exceptions.RequestException("Test network error")

        client = PlacesAPIClient(self.config)
        
        # 修复：跳过API密钥验证避免干扰重试测试
        client._api_key_validated = True
        
        grid_point = GridPoint(center=Coordinate(0, 0), radius=100, level=1)
        
        result = client.nearby_search(grid_point)

        # It should have called post MAX_RETRIES times
        self.assertEqual(mock_post.call_count, self.config.MAX_RETRIES)
        # It should return None
        self.assertIsNone(result)

@patch.dict(os.environ, {"GOOGLE_PLACES_API_KEY": "test_api_key"})
class TestPlacesAPIClientMockMode(unittest.TestCase):
    """测试Places API客户端的模拟模式功能"""

    def setUp(self):
        """设置模拟模式的配置和客户端"""
        # 创建启用模拟模式的配置
        self.config = ScanConfig(mock_mode=True, skip_system_checks=True)
        
        # 设置环境变量（虽然在模拟模式下不需要，但避免警告）
        self.env_patcher = patch.dict(os.environ, {"GOOGLE_PLACES_API_KEY": "test_api_key"})
        self.env_patcher.start()
        self.addCleanup(self.env_patcher.stop)
        
        self.client = PlacesAPIClient(self.config)

    def test_mock_mode_initialization(self):
        """测试模拟模式初始化"""
        self.assertTrue(self.client.mock_mode)
        self.assertTrue(hasattr(self.client, 'mock_templates'))
        self.assertIn('convenience_store', self.client.mock_templates)

    def test_mock_mode_skips_api_key_validation(self):
        """测试模拟模式跳过API密钥验证"""
        # 在模拟模式下，即使没有真实的API密钥也应该正常工作
        config = ScanConfig(mock_mode=True, skip_system_checks=True)
        
        with patch.dict(os.environ, {}, clear=True):  # 清除API密钥
            client = PlacesAPIClient(config)
            self.assertTrue(client._api_key_validated)

    def test_generate_mock_places(self):
        """测试模拟地点数据生成"""
        grid_point = GridPoint(
            center=Coordinate(latitude=34.0522, longitude=-118.2437),
            radius=1000,
            level=1
        )
        
        places = self.client._generate_mock_places(grid_point)
        
        # 验证基本结构
        self.assertIsInstance(places, list)
        self.assertGreaterEqual(len(places), 0)  # 可能为0个地点
        
        # 如果有地点，验证结构
        if places:
            place = places[0]
            self.assertIsInstance(place, PlaceData)
            self.assertTrue(place.place_id.startswith('mock_'))
            self.assertEqual(place.grid_point_id, grid_point.id)
            self.assertEqual(place.scan_level, grid_point.level)
            self.assertIn(place.types[0], self.config.PLACE_TYPES)

    def test_deterministic_seed_generation(self):
        """测试确定性种子生成"""
        grid_id = "test_grid_123"
        seed1 = self.client._generate_deterministic_seed(grid_id)
        seed2 = self.client._generate_deterministic_seed(grid_id)
        
        # 相同的grid_id应该产生相同的种子
        self.assertEqual(seed1, seed2)
        
        # 不同的grid_id应该产生不同的种子
        seed3 = self.client._generate_deterministic_seed("different_grid")
        self.assertNotEqual(seed1, seed3)

    def test_mock_places_consistency(self):
        """测试模拟地点数据的一致性"""
        grid_point = GridPoint(
            center=Coordinate(latitude=34.0522, longitude=-118.2437),
            radius=1000,
            level=1
        )
        
        # 多次生成应该得到相同的结果
        places1 = self.client._generate_mock_places(grid_point)
        places2 = self.client._generate_mock_places(grid_point)
        
        self.assertEqual(len(places1), len(places2))
        
        # 验证具体内容是否一致
        if places1:
            self.assertEqual(places1[0].place_id, places2[0].place_id)
            self.assertEqual(places1[0].name, places2[0].name)

    @patch('time.sleep')  # 模拟延迟，但在测试中跳过实际等待
    def test_nearby_search_mock_mode(self, mock_sleep):
        """测试模拟模式下的nearby_search方法"""
        grid_point = GridPoint(
            center=Coordinate(latitude=34.0522, longitude=-118.2437),
            radius=1000,
            level=2
        )
        
        result = self.client.nearby_search(grid_point)
        
        # 验证返回结果
        self.assertIsNotNone(result)
        self.assertIsInstance(result, list)
        
        # 验证性能统计更新
        self.assertGreater(self.client._success_count, 0)
        
        # 验证模拟延迟被调用
        mock_sleep.assert_called_once()

    def test_mock_mode_different_scan_levels(self):
        """测试不同扫描级别生成不同数量的地点"""
        base_grid = GridPoint(
            center=Coordinate(latitude=34.0522, longitude=-118.2437),
            radius=1000,
            level=1
        )
        
        # 测试不同级别
        levels_to_test = [1, 2, 3]
        place_counts = []
        
        for level in levels_to_test:
            grid_point = GridPoint(
                center=base_grid.center,
                radius=base_grid.radius,
                level=level
            )
            places = self.client._generate_mock_places(grid_point)
            place_counts.append(len(places))
        
        # 一般来说，级别越高应该生成更多地点（虽然有随机性）
        # 这里只验证都能成功生成
        for count in place_counts:
            self.assertGreaterEqual(count, 0)

    def test_performance_stats_in_mock_mode(self):
        """测试模拟模式下的性能统计"""
        initial_stats = self.client.get_performance_stats()
        
        grid_point = GridPoint(
            center=Coordinate(latitude=34.0522, longitude=-118.2437),
            radius=1000,
            level=1
        )
        
        # 执行几次模拟搜索
        with patch('time.sleep'):  # 跳过实际延迟
            for i in range(3):
                self.client.nearby_search(grid_point)
        
        final_stats = self.client.get_performance_stats()
        
        # 验证统计数据更新
        self.assertGreater(final_stats['total_requests'], initial_stats['total_requests'])
        self.assertGreater(final_stats['success_count'], initial_stats['success_count'])
        self.assertGreaterEqual(final_stats['success_rate'], 90)  # 模拟模式应该有高成功率

if __name__ == '__main__':
    unittest.main()
