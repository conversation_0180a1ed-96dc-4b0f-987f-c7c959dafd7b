#!/usr/bin/env python3
"""
配置验证测试脚本
测试ScanConfig的边界值、异常配置和错误处理
"""

import sys
import os
sys.path.append('src')

from src.config import ScanConfig
from decimal import Decimal

def test_coordinate_boundaries():
    """测试坐标边界值"""
    print("🧪 测试坐标边界值...")
    
    # 有效坐标
    valid_configs = [
        (90, 180),     # 最大值
        (-90, -180),   # 最小值
        (0, 0),        # 零值
        (31.23, 121.47)  # 正常值
    ]
    
    for lat, lng in valid_configs:
        try:
            config = ScanConfig(
                center_latitude=lat,
                center_longitude=lng,
                skip_system_checks=True
            )
            print(f"  ✅ 有效坐标: ({lat}, {lng})")
        except Exception as e:
            print(f"  ❌ 应该有效的坐标失败: ({lat}, {lng}) - {e}")
    
    # 无效坐标
    invalid_coords = [
        (91, 0),       # 纬度过大
        (-91, 0),      # 纬度过小
        (0, 181),      # 经度过大
        (0, -181),     # 经度过小
    ]
    
    for lat, lng in invalid_coords:
        try:
            config = ScanConfig(
                center_latitude=lat,
                center_longitude=lng,
                skip_system_checks=True
            )
            print(f"  ❌ 无效坐标应该失败: ({lat}, {lng})")
        except Exception as e:
            print(f"  ✅ 正确拒绝无效坐标: ({lat}, {lng}) - {str(e)[:50]}...")

def test_radius_boundaries():
    """测试半径边界值"""
    print("\n🧪 测试半径边界值...")
    
    # 有效半径
    valid_radii = [0.1, 1.0, 10.0, 100.0, 1000.0]
    
    for radius in valid_radii:
        try:
            config = ScanConfig(
                scan_radius_km=radius,
                skip_system_checks=True
            )
            print(f"  ✅ 有效半径: {radius}km")
        except Exception as e:
            print(f"  ❌ 应该有效的半径失败: {radius}km - {e}")
    
    # 无效半径
    invalid_radii = [0, -1, -10]
    
    for radius in invalid_radii:
        try:
            config = ScanConfig(
                scan_radius_km=radius,
                skip_system_checks=True
            )
            print(f"  ❌ 无效半径应该失败: {radius}km")
        except Exception as e:
            print(f"  ✅ 正确拒绝无效半径: {radius}km - {str(e)[:50]}...")

def test_budget_boundaries():
    """测试预算边界值"""
    print("\n🧪 测试预算边界值...")
    
    # 有效预算
    valid_budgets = [0.01, 1.0, 100.0, 1000.0]
    
    for budget in valid_budgets:
        try:
            config = ScanConfig(
                MAX_BUDGET=budget,
                skip_system_checks=True
            )
            print(f"  ✅ 有效预算: ${budget}")
        except Exception as e:
            print(f"  ❌ 应该有效的预算失败: ${budget} - {e}")
    
    # 无效预算
    invalid_budgets = [0, -1, -100]
    
    for budget in invalid_budgets:
        try:
            config = ScanConfig(
                MAX_BUDGET=budget,
                skip_system_checks=True
            )
            print(f"  ❌ 无效预算应该失败: ${budget}")
        except Exception as e:
            print(f"  ✅ 正确拒绝无效预算: ${budget} - {str(e)[:50]}...")

def test_grid_spacing_boundaries():
    """测试网格间距边界值"""
    print("\n🧪 测试网格间距边界值...")
    
    # 测试网格间距逻辑关系
    test_cases = [
        # (macro, fine, expected_result)
        (2.0, 1.0, True),    # 正常：macro > fine
        (1.0, 2.0, False),   # 异常：macro < fine
        (1.0, 1.0, False),   # 边界：macro = fine
        (0.1, 0.05, True),   # 小值但正常
    ]
    
    for macro, fine, should_pass in test_cases:
        try:
            config = ScanConfig(
                MACRO_GRID_SPACING=macro,
                FINE_GRID_SPACING=fine,
                skip_system_checks=True
            )
            if not should_pass:
                print(f"  ❌ 应该失败的网格配置通过了: macro={macro}, fine={fine}")
            else:
                print(f"  ✅ 有效网格配置: macro={macro}, fine={fine}")
        except Exception as e:
            if should_pass:
                print(f"  ❌ 应该有效的网格配置失败: macro={macro}, fine={fine} - {str(e)[:50]}...")
            else:
                print(f"  ✅ 正确拒绝无效网格配置: macro={macro}, fine={fine}")

def test_recursion_parameters():
    """测试递归参数"""
    print("\n🧪 测试递归参数...")
    
    # 有效递归参数
    valid_recursion = [
        (1, 5, 100),      # 最小深度
        (3, 20, 200),     # 正常值
        (5, 50, 500),     # 较大值
    ]
    
    for depth, trigger, radius in valid_recursion:
        try:
            config = ScanConfig(
                MAX_RECURSION_DEPTH=depth,
                RECURSION_TRIGGER_COUNT=trigger,
                MIN_SEARCH_RADIUS=radius,
                skip_system_checks=True
            )
            print(f"  ✅ 有效递归参数: depth={depth}, trigger={trigger}, radius={radius}")
        except Exception as e:
            print(f"  ❌ 应该有效的递归参数失败: {e}")

def test_api_parameters():
    """测试API参数"""
    print("\n🧪 测试API参数...")
    
    # 测试空列表
    try:
        config = ScanConfig(
            PLACE_TYPES=[],
            skip_system_checks=True
        )
        print("  ❌ 空的PLACE_TYPES应该失败")
    except Exception as e:
        print(f"  ✅ 正确拒绝空的PLACE_TYPES: {str(e)[:50]}...")
    
    # 测试有效的place types
    try:
        config = ScanConfig(
            PLACE_TYPES=['convenience_store', 'supermarket'],
            skip_system_checks=True
        )
        print("  ✅ 有效的PLACE_TYPES")
    except Exception as e:
        print(f"  ❌ 有效的PLACE_TYPES失败: {e}")

def test_config_serialization():
    """测试配置序列化"""
    print("\n🧪 测试配置序列化...")
    
    try:
        # 创建配置
        original_config = ScanConfig(
            center_latitude=31.23,
            center_longitude=121.47,
            scan_radius_km=5.0,
            skip_system_checks=True
        )
        
        # 转换为字典
        config_dict = original_config.to_dict()
        print("  ✅ 配置序列化成功")
        
        # 检查关键字段
        required_fields = ['center_latitude', 'center_longitude', 'scan_radius_km']
        for field in required_fields:
            if field not in config_dict:
                print(f"  ❌ 缺少必需字段: {field}")
            else:
                print(f"  ✅ 包含必需字段: {field}")
                
    except Exception as e:
        print(f"  ❌ 配置序列化失败: {e}")

def main():
    """运行所有配置验证测试"""
    print("🔍 开始配置验证测试")
    print("=" * 50)
    
    test_coordinate_boundaries()
    test_radius_boundaries()
    test_budget_boundaries()
    test_grid_spacing_boundaries()
    test_recursion_parameters()
    test_api_parameters()
    test_config_serialization()
    
    print("\n" + "=" * 50)
    print("✅ 配置验证测试完成")

if __name__ == "__main__":
    main() 