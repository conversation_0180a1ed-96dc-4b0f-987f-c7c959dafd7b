#!/usr/bin/env python3
"""
网格参数可视化工具启动器

这个工具帮助你实时调整和优化网格扫描参数。

使用方法:
    python start_parameter_visualizer.py [--port 8091] [--host localhost]
"""

import http.server
import socketserver
import webbrowser
import os
import sys
import argparse
from pathlib import Path

def find_visualizer_file():
    """查找可视化HTML文件"""
    possible_paths = [
        "grid_parameter_visualizer.html",  # 同目录下（src/）
        "./grid_parameter_visualizer.html",  # 相对路径
        "../grid_parameter_visualizer.html",  # 上级目录
        "src/grid_parameter_visualizer.html",  # 如果从根目录运行
        os.path.join(os.path.dirname(__file__), "grid_parameter_visualizer.html")  # 脚本同目录
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            print(f"✅ 找到可视化文件: {path}")
            return path
    
    return None

def start_server(port=8090, host='localhost'):
    """启动HTTP服务器"""
    # 确保在正确的目录
    visualizer_path = find_visualizer_file()
    if not visualizer_path:
        print("❌ 找不到 grid_parameter_visualizer.html 文件")
        print("请确保文件存在于以下位置之一:")
        print("  - src/grid_parameter_visualizer.html (推荐)")
        print("  - ./grid_parameter_visualizer.html")
        print("  - ../grid_parameter_visualizer.html")
        print(f"当前工作目录: {os.getcwd()}")
        print(f"脚本所在目录: {os.path.dirname(os.path.abspath(__file__))}")
        sys.exit(1)
    
    # 切换到包含HTML文件的目录
    html_dir = os.path.dirname(visualizer_path) or '.'
    os.chdir(html_dir)
    
    # 获取HTML文件名
    html_filename = os.path.basename(visualizer_path)
    
    class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
        def end_headers(self):
            # 添加CORS头，允许本地访问
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            super().end_headers()
        
        def do_GET(self):
            # 如果访问根路径，重定向到可视化器
            if self.path == '/':
                self.path = '/' + html_filename
            super().do_GET()
    
    try:
        with socketserver.TCPServer((host, port), CustomHTTPRequestHandler) as httpd:
            print(f"🚀 网格参数可视化工具启动中...")
            print(f"📍 服务器地址: http://{host}:{port}")
            print(f"📁 服务目录: {os.getcwd()}")
            print(f"📄 可视化文件: {html_filename}")
            print(f"🌐 浏览器将自动打开，如果没有打开请手动访问上述地址")
            print(f"💡 使用 Ctrl+C 停止服务器")
            print("-" * 60)
            
            # 自动打开浏览器
            url = f"http://{host}:{port}"
            webbrowser.open(url)
            
            # 启动服务器
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 48 or "Address already in use" in str(e):
            print(f"❌ 端口 {port} 已被占用")
            print(f"💡 请尝试其他端口: python {sys.argv[0]} --port {port + 1}")
        else:
            print(f"❌ 启动服务器失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print(f"\n✅ 服务器已停止")
        sys.exit(0)

def main():
    parser = argparse.ArgumentParser(
        description="启动网格参数可视化工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用说明:
  这个工具提供了一个交互式界面，让你可以：
  
  📊 实时调整网格参数:
    • Level 1 (宏网格): 间距和搜索半径
    • Level 2 (精网格): 间距、半径和触发阈值  
    • Level 3 (增强网格): 密集扫描参数
  
  📈 即时预览效果:
    • 可视化网格分布
    • 计算覆盖率和重叠度
    • 估算API调用成本
  
  💾 配置管理:
    • 导出优化后的配置文件
    • 加载推荐配置
    • 重置为默认值
  
  使用技巧:
    • 搜索半径建议略小于网格间距
    • 观察橙色圆圈重叠度来调整Level 2参数
    • 成本估算帮助控制预算

示例:
  python src/start_parameter_visualizer.py
  python src/start_parameter_visualizer.py --port 8000
  python src/start_parameter_visualizer.py --host 0.0.0.0 --port 8090
        """
    )
    
    parser.add_argument(
        '--port', '-p',
        type=int,
        default=8090,
        help='HTTP服务器端口 (默认: 8090)'
    )
    
    parser.add_argument(
        '--host', '-H',
        type=str,
        default='localhost',
        help='HTTP服务器主机 (默认: localhost)'
    )
    
    args = parser.parse_args()
    
    print("🔧 网格参数可视化调节器")
    print("=" * 40)
    print("这个工具帮助你找到最优的网格扫描参数组合")
    print()
    
    start_server(args.port, args.host)

if __name__ == "__main__":
    main() 