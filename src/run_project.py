#!/usr/bin/env python3
"""
项目执行入口 - 运行一个完整的扫描项目
"""
import argparse
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.grid_executor import GridExecutor

def main():
    parser = argparse.ArgumentParser(description="运行一个完整的扫描项目")
    parser.add_argument("--project", type=str, required=True, help="要运行的项目名称")
    parser.add_argument("--all", action="store_true", help="执行所有待处理的Grid")
    parser.add_argument("--grid-ids", type=str, help="逗号分隔的Grid ID列表，用于指定执行")
    parser.add_argument("--mock-mode", action="store_true", help="使用模拟模式运行（不消耗API额度）")
    parser.add_argument("--workers", type=int, default=3, help="并发执行的工作线程数")
    args = parser.parse_args()

    print(f"🚀 开始执行项目: {args.project}")
    
    executor = GridExecutor(args.project, mock_mode=args.mock_mode)
    
    # Execute all pending grids, starting from the lowest resolution
    for status in ["pending", "failed"]: # Retry failed grids as well
        print(f"--- 检查 {status} 状态的Grid ---")
        pending_grids = executor.project_manager.get_grids_by_status(args.project, status)
        
        if pending_grids:
            print(f"发现 {len(pending_grids)} 个 {status} Grid，开始执行...")
            executor.execute_grids_batch(pending_grids, max_workers=args.workers)
        else:
            print(f"没有 {status} 状态的Grid")

    print(f"✅ 项目 '{args.project}' 执行完成")

if __name__ == "__main__":
    main()
