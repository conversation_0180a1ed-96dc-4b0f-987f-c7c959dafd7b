#!/usr/bin/env python3
"""
测试不同分辨率转换
验证从res5到res7的转换是否正确，以及其他分辨率转换
"""

import h3
import json
import math
from pathlib import Path

def test_resolution_conversion():
    """测试分辨率转换"""
    
    print("🔍 测试H3分辨率转换")
    print("=" * 60)
    
    # 测试基本分辨率转换
    print("\n1. 基本分辨率转换测试:")
    print("-" * 40)
    
    test_lat, test_lng = 34.0522, -118.2437
    
    # 测试所有可能的转换组合
    conversions = [
        (5, 6), (5, 7), (5, 8), (5, 9),
        (6, 7), (6, 8), (6, 9),
        (7, 8), (7, 9),
        (8, 9)
    ]
    
    for parent_res, child_res in conversions:
        try:
            parent_h3 = h3.latlng_to_cell(test_lat, test_lng, parent_res)
            children = h3.cell_to_children(parent_h3, child_res)
            
            print(f"res {parent_res} → res {child_res}:")
            print(f"  父网格: {parent_h3}")
            print(f"  子网格数量: {len(children)}")
            
            # 验证子网格数量的理论值
            expected_count = 7 ** (child_res - parent_res)
            if len(children) == expected_count:
                print(f"  ✅ 子网格数量正确 (理论值: {expected_count})")
            else:
                print(f"  ❌ 子网格数量异常 (期望: {expected_count}, 实际: {len(children)})")
            
            # 验证前几个子网格
            valid_children = 0
            for child_h3 in list(children)[:5]:
                if h3.is_valid_cell(child_h3) and h3.get_resolution(child_h3) == child_res:
                    valid_children += 1
            
            print(f"  验证结果: {valid_children}/5 个子网格有效")
            
        except Exception as e:
            print(f"res {parent_res} → res {child_res}: ❌ 转换失败 - {e}")
        
        print()
    
    # 测试项目中的实际转换
    print("\n2. 项目实际转换测试:")
    print("-" * 40)
    
    test_projects = ["test-strong-overlap"]
    
    for project_name in test_projects:
        print(f"\n项目: {project_name}")
        
        # 加载项目配置
        config_file = Path("projects") / project_name / "project_config.json"
        if not config_file.exists():
            print(f"  ❌ 配置文件不存在")
            continue
            
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 加载Level 5网格
        grids_file = Path("projects") / project_name / "grids" / "level5_grids.json"
        if not grids_file.exists():
            print(f"  ❌ Level 5网格文件不存在")
            continue
            
        with open(grids_file, 'r', encoding='utf-8') as f:
            level5_grids = json.load(f)
        
        print(f"  Level 5网格数量: {len(level5_grids)}")
        
        # 测试Level 5 → Level 7转换
        res5_to_res7_total = 0
        conversion_errors = 0
        
        for i, grid in enumerate(level5_grids[:3]):  # 测试前3个网格
            parent_h3 = grid['h3_index']
            
            try:
                children = h3.cell_to_children(parent_h3, config['h3_res_level2'])
                res5_to_res7_total += len(children)
                
                print(f"  网格 {i+1} ({parent_h3}): {len(children)} 个Level 7子网格")
                
                # 验证子网格有效性
                valid_count = 0
                for child_h3 in list(children)[:10]:  # 验证前10个
                    if h3.is_valid_cell(child_h3) and h3.get_resolution(child_h3) == config['h3_res_level2']:
                        valid_count += 1
                
                print(f"    验证: {valid_count}/10 个子网格有效")
                
            except Exception as e:
                conversion_errors += 1
                print(f"  ❌ 网格 {i+1} 转换失败: {e}")
        
        print(f"  总计Level 7子网格: {res5_to_res7_total}")
        print(f"  转换错误: {conversion_errors}")
        
        # 测试Level 7 → Level 8转换（如果有Level 7网格）
        level7_file = Path("projects") / project_name / "grids" / "level7_grids.json"
        if level7_file.exists():
            with open(level7_file, 'r', encoding='utf-8') as f:
                level7_grids = json.load(f)
            
            print(f"  Level 7网格数量: {len(level7_grids)}")
            
            res7_to_res8_total = 0
            for i, grid in enumerate(level7_grids[:3]):  # 测试前3个
                parent_h3 = grid['h3_index']
                
                try:
                    children = h3.cell_to_children(parent_h3, config['h3_res_level3'])
                    res7_to_res8_total += len(children)
                    
                    print(f"  Level 7网格 {i+1}: {len(children)} 个Level 8子网格")
                    
                except Exception as e:
                    print(f"  ❌ Level 7网格 {i+1} 转换失败: {e}")
            
            print(f"  总计Level 8子网格: {res7_to_res8_total}")
    
    # 测试转换的几何一致性
    print("\n3. 转换几何一致性测试:")
    print("-" * 40)
    
    parent_h3 = h3.latlng_to_cell(34.0522, -118.2437, 5)
    parent_boundary = h3.cell_to_boundary(parent_h3)
    
    print(f"父网格 (res 5): {parent_h3}")
    print(f"父网格边界点数: {len(parent_boundary)}")
    
    # 计算父网格的大致面积（简化计算）
    parent_lats = [lat for lat, lng in parent_boundary]
    parent_lngs = [lng for lat, lng in parent_boundary]
    parent_area_approx = (max(parent_lats) - min(parent_lats)) * (max(parent_lngs) - min(parent_lngs))
    
    print(f"父网格大致面积: {parent_area_approx:.6f} 平方度")
    
    # 测试子网格是否完全覆盖父网格
    for child_res in [6, 7, 8]:
        children = h3.cell_to_children(parent_h3, child_res)
        
        print(f"\nres 5 → res {child_res}:")
        print(f"  子网格数量: {len(children)}")
        
        # 计算所有子网格的总面积
        total_child_area = 0
        valid_children = 0
        
        for child_h3 in list(children)[:20]:  # 测试前20个
            try:
                child_boundary = h3.cell_to_boundary(child_h3)
                child_lats = [lat for lat, lng in child_boundary]
                child_lngs = [lng for lat, lng in child_boundary]
                child_area = (max(child_lats) - min(child_lats)) * (max(child_lngs) - min(child_lngs))
                total_child_area += child_area
                valid_children += 1
            except Exception as e:
                print(f"    ❌ 子网格处理失败: {e}")
        
        if valid_children > 0:
            avg_child_area = total_child_area / valid_children
            estimated_total_area = avg_child_area * len(children)
            
            print(f"  平均子网格面积: {avg_child_area:.8f} 平方度")
            print(f"  估计总面积: {estimated_total_area:.6f} 平方度")
            print(f"  面积比例: {estimated_total_area/parent_area_approx:.2f}")
            
            if 0.8 <= estimated_total_area/parent_area_approx <= 1.2:
                print(f"  ✅ 面积覆盖合理")
            else:
                print(f"  ⚠️  面积覆盖可能有问题")
    
    # 测试反向转换
    print("\n4. 反向转换测试:")
    print("-" * 40)
    
    # 测试子网格是否能正确找到父网格
    parent_h3 = h3.latlng_to_cell(34.0522, -118.2437, 5)
    children = h3.cell_to_children(parent_h3, 7)
    
    print(f"父网格 (res 5): {parent_h3}")
    print(f"子网格数量 (res 7): {len(children)}")
    
    # 测试前几个子网格的父网格
    correct_parents = 0
    for i, child_h3 in enumerate(list(children)[:10]):
        try:
            recovered_parent = h3.cell_to_parent(child_h3, 5)
            
            if recovered_parent == parent_h3:
                correct_parents += 1
            else:
                print(f"  ❌ 子网格 {i+1}: 父网格不匹配")
                print(f"      期望: {parent_h3}")
                print(f"      实际: {recovered_parent}")
                
        except Exception as e:
            print(f"  ❌ 子网格 {i+1} 反向转换失败: {e}")
    
    print(f"反向转换结果: {correct_parents}/10 个子网格正确找到父网格")
    
    # 测试边界情况的转换
    print("\n5. 边界情况转换测试:")
    print("-" * 40)
    
    edge_cases = [
        ("极地", 89.0, 0.0),
        ("赤道", 0.0, 0.0),
        ("日期变更线", 0.0, 179.0),
    ]
    
    for case_name, lat, lng in edge_cases:
        print(f"\n{case_name}: ({lat}, {lng})")
        
        try:
            parent_h3 = h3.latlng_to_cell(lat, lng, 5)
            children = h3.cell_to_children(parent_h3, 7)
            
            print(f"  父网格: {parent_h3}")
            print(f"  子网格数量: {len(children)}")
            
            # 验证几个子网格
            valid_count = 0
            for child_h3 in list(children)[:5]:
                if h3.is_valid_cell(child_h3) and h3.get_resolution(child_h3) == 7:
                    valid_count += 1
            
            print(f"  验证结果: {valid_count}/5 个子网格有效")
            
        except Exception as e:
            print(f"  ❌ 转换失败: {e}")

if __name__ == "__main__":
    test_resolution_conversion()
