# H3网格参数可视化调节器使用指南

## 🎯 工具概述

H3网格参数可视化调节器是一个交互式工具，帮助你找到最优的H3六边形网格扫描参数组合。通过实时调整参数并即时预览效果，你可以在实际部署前优化扫描策略。

### 🔷 H3六边形网格系统
本工具使用**H3六边形网格系统**进行精确的地理网格划分，确保：
- ✅ 均匀的六边形网格覆盖
- ✅ 分层级的网格分辨率控制
- ✅ 精确的地理边界处理
- ✅ 全球统一的网格标准

## 🚀 快速启动

### 启动可视化工具

```bash
# 基本启动
python start_parameter_visualizer.py

# 指定端口
python start_parameter_visualizer.py --port 8000

# 允许外部访问
python start_parameter_visualizer.py --host 0.0.0.0 --port 8080
```

启动后浏览器会自动打开，或手动访问显示的地址。

## 🎛️ 界面说明

### 左侧参数面板

#### Level 1 H3网格 (红色六边形)
- **H3分辨率**: H3网格分辨率级别 (3-7)，数值越大网格越小越密集
- **搜索半径**: 每个Level 1网格的搜索范围 (2000-10000米)

#### Level 2 H3网格 (橙色六边形)
- **H3分辨率**: Level 2网格分辨率，应大于Level 1 (5-9)
- **搜索半径**: Level 2网格搜索半径 (500-2000米)
- **触发阈值**: 发现多少个地点时触发Level 3扫描 (10-50个)

#### Level 3 增强网格 (绿色圆圈)
- **网格间距**: 增强扫描的网格间距 (0.2-1.5km)
- **搜索半径**: 增强扫描搜索半径 (0.1-1km)

### 右侧地图预览

- **蓝色边界**: 扫描区域范围
- **黑点**: 扫描中心点
- **红色圆圈**: Level 1 宏网格覆盖
- **橙色圆圈**: Level 2 精网格覆盖
- **绿色圆圈**: Level 3 增强网格覆盖

### 统计信息面板

- **总网格数**: 所有级别网格点总数
- **覆盖率**: 搜索区域重叠程度
- **Level 1/2**: 各级别网格数量
- **预估成本**: 基于API调用的估算费用

## 📊 参数调优策略

### 1. 基础原则

```
搜索半径 < 网格间距
```
这样可以避免过度重叠，又确保完整覆盖。

### 2. 推荐参数比例

```
Level 1 间距 : Level 2 间距 = 5:1
Level 2 间距 : Level 3 间距 = 2:1

Level 1 半径 : Level 1 间距 = 0.7:1
Level 2 半径 : Level 2 间距 = 0.7:1
```

### 3. 不同场景的优化策略

#### 🏙️ 密集城市区域
```
Level 1: 间距 5km,  半径 3.5km
Level 2: 间距 1.2km, 半径 0.8km
Level 3: 间距 0.6km, 半径 0.4km
触发阈值: 15个
```

#### 🏘️ 郊区混合区域  
```
Level 1: 间距 7km,  半径 5km
Level 2: 间距 1.4km, 半径 1km
Level 3: 间距 0.7km, 半径 0.5km
触发阈值: 20个
```

#### 🌾 乡村稀疏区域
```
Level 1: 间距 10km, 半径 7km
Level 2: 间距 2km,   半径 1.5km
Level 3: 间距 1km,   半径 0.7km
触发阈值: 25个
```

## 🎚️ 实际调优步骤

### 步骤 1: 设置Level 1基础覆盖

1. 调整**宏网格间距**，观察红色圆圈分布
2. 确保红色圆圈基本覆盖整个扫描区域
3. 调整**宏网格半径**，控制重叠度在20-30%

### 步骤 2: 优化Level 2精细扫描

1. 调整**精网格间距**，观察橙色圆圈密度
2. 确保橙色圆圈不会过度重叠
3. 调整**触发阈值**，控制Level 3触发频率

### 步骤 3: 微调Level 3增强扫描

1. 调整**增强网格间距**，平衡覆盖与成本
2. 观察绿色圆圈，确保热点区域覆盖充分

### 步骤 4: 成本与效果平衡

1. 监控**预估成本**，确保在预算范围内
2. 检查**覆盖率**，确保达到预期效果
3. 如出现成本警告，适当增大间距或减小半径

## 💡 优化技巧

### 观察重叠模式

- **过度重叠**: 圆圈大量重叠，浪费API调用
- **覆盖不足**: 圆圈间有明显空隙，可能遗漏POI
- **理想状态**: 轻微重叠，确保无遗漏

### 成本控制策略

1. **优先调整间距**: 间距对网格数量影响最大
2. **适度减小半径**: 在保证覆盖的前提下减小搜索半径
3. **提高触发阈值**: 减少Level 3触发，控制总网格数

### 参数验证方法

1. **导出配置**: 点击"导出配置文件"保存优化结果
2. **小范围测试**: 在小区域先测试参数效果
3. **逐步扩大**: 确认无误后再应用到完整区域

## 🔧 配置文件使用

### 导出配置

调节好参数后，点击"导出配置文件"按钮，会下载一个JSON文件包含:

```json
{
  "MACRO_GRID_SPACING": 7.0,
  "MACRO_SEARCH_RADIUS": 5000,
  "FINE_GRID_SPACING": 1.4,
  "FINE_SEARCH_RADIUS": 1000,
  "RECURSION_TRIGGER_COUNT": 20,
  "RECURSION_SPACING_FACTOR": 0.5,
  "RECURSION_RADIUS_FACTOR": 0.5
}
```

### 应用配置

将导出的参数更新到 `src/config.py` 文件中的对应字段。

## ⚠️ 注意事项

### 参数约束

1. **网格间距**: Level 1 > Level 2 > Level 3
2. **搜索半径**: 建议 < 对应的网格间距
3. **触发阈值**: 过低会造成过度扫描，过高可能遗漏热点

### 成本估算说明

- 预估成本基于每个网格点一次API调用
- 实际成本会因为地点数量和递归深度而变化
- 建议预留20-30%的成本缓冲

### 地理因素考虑

- **纬度影响**: 高纬度地区经度距离会缩短
- **地形影响**: 山区、水域可能影响实际可访问性
- **人口密度**: 不同区域的POI密度差异很大

## 🎨 界面功能

### 操作按钮

- **导出配置文件**: 保存当前参数设置
- **重置为默认值**: 恢复系统默认参数
- **加载推荐配置**: 应用针对城市环境的推荐参数

### 实时反馈

- 滑动任何参数滑块都会立即更新地图显示
- 统计数据实时计算并显示
- 成本超限会显示警告提示

## 🔍 故障排除

### 常见问题

**Q: 网格显示不完整？**
A: 检查扫描半径设置，可能需要增大显示范围。

**Q: 成本过高怎么办？**
A: 增大网格间距，减小搜索半径，或提高触发阈值。

**Q: 覆盖率显示异常？**
A: 覆盖率计算基于简化模型，仅供参考。

**Q: 导出的配置文件在哪里？**
A: 通常下载到浏览器默认下载文件夹中。

---

通过这个可视化工具，你可以在实际部署前找到最适合你扫描区域的参数组合，实现成本和效果的最佳平衡。 