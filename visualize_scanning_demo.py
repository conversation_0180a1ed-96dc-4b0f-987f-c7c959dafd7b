#!/usr/bin/env python3
"""
H3 扫描功能演示

专门展示扫描桥接器的工作原理：
1. 单网格扫描
2. 多层级扫描
3. 钻入下一层的逻辑
"""

import sys
import os
import json
from datetime import datetime

# 添加 src 目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

import h3
from h3_service import H3Converter, H3Visualizer, H3ScannerBridge
from h3_service.h3_scanner_bridge import ScanConfig, GridSpec


def create_scanning_demo():
    """创建扫描演示数据"""
    print("🔄 创建扫描演示...")
    
    # 测试区域：洛杉矶市中心
    center_lat, center_lng = 34.0522, -118.2437
    
    # 创建扫描配置
    config = H3ScannerBridge.create_default_config(
        place_types=["convenience_store", "restaurant", "gas_station"],
        mock_mode=True
    )
    bridge = H3ScannerBridge(config)
    
    demo_data = {
        "center": {"lat": center_lat, "lng": center_lng},
        "config": {
            "place_types": config.place_types,
            "layer_config": config.layer_config,
            "mock_mode": config.mock_mode
        },
        "scanning_demo": []
    }
    
    # 演示三层扫描
    for level in range(1, 4):  # Level 1, 2, 3
        layer_config = config.layer_config[level - 1]
        h3_res = layer_config["h3_res"]
        search_radius = layer_config["search_radius"]
        
        print(f"  演示 Level {level} (分辨率 {h3_res}, 半径 {search_radius}m)...")
        
        # 生成该层级的网格
        grids = H3Converter.area_to_grids(center_lat, center_lng, 5.0, h3_res)
        
        # 选择前几个网格进行扫描
        demo_grids = grids[:min(5, len(grids))]
        
        level_results = []
        for grid_id in demo_grids:
            # 执行扫描
            result = bridge.scan_single_grid(grid_id, level)
            
            # 获取网格可视化信息
            coverage_info = H3Visualizer.get_grid_coverage_info(grid_id)
            
            # 模拟地点在网格内的分布
            places_with_locations = []
            if result.places:
                for i, place in enumerate(result.places[:10]):  # 最多显示10个地点
                    # 在网格边界内随机生成位置（简化版）
                    center = coverage_info["center"]
                    # 简单偏移来模拟地点位置
                    offset_lat = (i % 3 - 1) * 0.001
                    offset_lng = (i % 3 - 1) * 0.001
                    
                    places_with_locations.append({
                        "name": place["name"],
                        "id": place["id"],
                        "lat": center["lat"] + offset_lat,
                        "lng": center["lng"] + offset_lng,
                        "types": place.get("types", [])
                    })
            
            level_results.append({
                "grid_id": grid_id,
                "center": coverage_info["center"],
                "boundary": coverage_info["boundary"],
                "places_count": len(result.places),
                "should_drill_down": result.should_drill_down,
                "success": result.success,
                "places": places_with_locations,
                "search_radius": search_radius,
                "metadata": result.metadata
            })
        
        demo_data["scanning_demo"].append({
            "level": level,
            "h3_resolution": h3_res,
            "search_radius": search_radius,
            "grid_count": len(demo_grids),
            "results": level_results
        })
    
    # 获取最终统计
    demo_data["final_stats"] = bridge.get_scanner_stats()
    
    return demo_data


def generate_scanning_html(demo_data):
    """生成扫描演示HTML"""
    print("🎨 生成扫描演示HTML...")
    
    html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>H3 扫描功能演示</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; }}
        #map {{ height: 700px; width: 100%; margin-bottom: 20px; }}
        .info-panel {{ background: #f0f0f0; padding: 15px; border-radius: 5px; margin-bottom: 10px; }}
        .controls {{ margin-bottom: 20px; }}
        .controls button {{ margin: 5px; padding: 8px 15px; cursor: pointer; border: none; border-radius: 3px; }}
        .level-1 {{ background-color: #ff4444; color: white; }}
        .level-2 {{ background-color: #44ff44; color: black; }}
        .level-3 {{ background-color: #4444ff; color: white; }}
        .show-all {{ background-color: #666; color: white; }}
        .clear-all {{ background-color: #ccc; color: black; }}
        .stats {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px; }}
        .stat-card {{ background: white; padding: 15px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .legend {{ background: white; padding: 10px; border-radius: 5px; margin-bottom: 10px; }}
        .legend-item {{ display: flex; align-items: center; margin: 5px 0; }}
        .legend-color {{ width: 20px; height: 20px; margin-right: 10px; border-radius: 3px; }}
    </style>
</head>
<body>
    <h1>🔍 H3 扫描功能演示</h1>
    
    <div class="info-panel">
        <h3>📊 演示概览</h3>
        <p><strong>测试中心:</strong> ({demo_data['center']['lat']:.4f}, {demo_data['center']['lng']:.4f})</p>
        <p><strong>地点类型:</strong> {', '.join(demo_data['config']['place_types'])}</p>
        <p><strong>Mock模式:</strong> {'是' if demo_data['config']['mock_mode'] else '否'}</p>
        <p><strong>生成时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="legend">
        <h4>🎨 图例</h4>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #ff4444;"></div>
            <span>Level 1 - 分辨率 {demo_data['scanning_demo'][0]['h3_resolution']}, 半径 {demo_data['scanning_demo'][0]['search_radius']}m</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #44ff44;"></div>
            <span>Level 2 - 分辨率 {demo_data['scanning_demo'][1]['h3_resolution']}, 半径 {demo_data['scanning_demo'][1]['search_radius']}m</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #4444ff;"></div>
            <span>Level 3 - 分辨率 {demo_data['scanning_demo'][2]['h3_resolution']}, 半径 {demo_data['scanning_demo'][2]['search_radius']}m</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #ff6600;"></div>
            <span>🍕 地点标记</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: rgba(255, 255, 0, 0.3); border: 2px solid #ffff00;"></div>
            <span>🔍 搜索半径</span>
        </div>
    </div>
    
    <div class="controls">
        <button class="level-1" onclick="showLevel(1)">显示 Level 1</button>
        <button class="level-2" onclick="showLevel(2)">显示 Level 2</button>
        <button class="level-3" onclick="showLevel(3)">显示 Level 3</button>
        <button class="show-all" onclick="showAll()">显示全部</button>
        <button class="clear-all" onclick="clearAll()">清除全部</button>
    </div>
    
    <div id="map"></div>
    
    <div class="stats">
"""
    
    # 为每个层级生成统计卡片
    for level_data in demo_data["scanning_demo"]:
        level = level_data["level"]
        total_places = sum(r["places_count"] for r in level_data["results"])
        drill_down_count = sum(1 for r in level_data["results"] if r["should_drill_down"])
        
        html_content += f"""
        <div class="stat-card">
            <h4>📊 Level {level} 统计</h4>
            <ul>
                <li>H3分辨率: {level_data['h3_resolution']}</li>
                <li>搜索半径: {level_data['search_radius']}m</li>
                <li>扫描网格: {level_data['grid_count']} 个</li>
                <li>找到地点: {total_places} 个</li>
                <li>需要钻入: {drill_down_count} 个网格</li>
            </ul>
        </div>
"""
    
    html_content += f"""
        <div class="stat-card">
            <h4>🎯 总体统计</h4>
            <ul>
                <li>总API调用: {demo_data['final_stats']['api_calls_made']}</li>
                <li>唯一地点: {demo_data['final_stats']['unique_places_found']}</li>
                <li>配置层级: {demo_data['final_stats']['config']['layer_count']}</li>
                <li>最大重试: {demo_data['final_stats']['config']['max_retries']}</li>
            </ul>
        </div>
    </div>
    
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        // 初始化地图
        var map = L.map('map').setView([{demo_data['center']['lat']}, {demo_data['center']['lng']}], 12);
        
        L.tileLayer('https://{{s}}.tile.openstreetmap.org/{{z}}/{{x}}/{{y}}.png', {{
            attribution: '© OpenStreetMap contributors'
        }}).addTo(map);
        
        // 添加中心点标记
        L.marker([{demo_data['center']['lat']}, {demo_data['center']['lng']}])
            .addTo(map)
            .bindPopup('🎯 测试中心');
        
        // 存储图层
        var layers = {{
            level1: L.layerGroup(),
            level2: L.layerGroup(),
            level3: L.layerGroup()
        }};
        
        // 演示数据
        var demoData = {json.dumps(demo_data, indent=2)};
        
        // 颜色配置
        var levelColors = {{
            1: '#ff4444',
            2: '#44ff44',
            3: '#4444ff'
        }};
        
        // 创建图层
        function createLayers() {{
            demoData.scanning_demo.forEach(function(levelData) {{
                var level = levelData.level;
                var color = levelColors[level];
                var layerGroup = layers['level' + level];
                
                levelData.results.forEach(function(result) {{
                    // 创建网格多边形
                    var polygon = L.polygon(result.boundary, {{
                        color: color,
                        weight: 2,
                        fillOpacity: 0.2
                    }});
                    
                    // 创建搜索半径圆圈
                    var searchCircle = L.circle([result.center.lat, result.center.lng], {{
                        color: '#ffff00',
                        fillColor: '#ffff00',
                        fillOpacity: 0.1,
                        weight: 2,
                        radius: result.search_radius
                    }});
                    
                    // 创建弹出窗口内容
                    var popupContent = '<b>Level ' + level + ' 扫描结果</b><br>' +
                        'Grid ID: ' + result.grid_id + '<br>' +
                        '搜索半径: ' + result.search_radius + 'm<br>' +
                        '找到地点: ' + result.places_count + ' 个<br>' +
                        '需要钻入: ' + (result.should_drill_down ? '是' : '否') + '<br>' +
                        '成功: ' + (result.success ? '是' : '否');
                    
                    if (result.places.length > 0) {{
                        popupContent += '<br><br><b>地点列表:</b>';
                        result.places.forEach(function(place, index) {{
                            popupContent += '<br>' + (index + 1) + '. ' + place.name;
                        }});
                    }}
                    
                    polygon.bindPopup(popupContent);
                    searchCircle.bindPopup(popupContent);
                    
                    layerGroup.addLayer(polygon);
                    layerGroup.addLayer(searchCircle);
                    
                    // 添加地点标记
                    result.places.forEach(function(place) {{
                        var marker = L.circleMarker([place.lat, place.lng], {{
                            color: '#ff6600',
                            fillColor: '#ff6600',
                            fillOpacity: 0.8,
                            radius: 5
                        }}).bindPopup(
                            '<b>' + place.name + '</b><br>' +
                            'ID: ' + place.id + '<br>' +
                            '位置: (' + place.lat.toFixed(4) + ', ' + place.lng.toFixed(4) + ')'
                        );
                        
                        layerGroup.addLayer(marker);
                    }});
                }});
            }});
        }}
        
        // 控制函数
        function showLevel(level) {{
            clearAll();
            layers['level' + level].addTo(map);
        }}
        
        function showAll() {{
            Object.values(layers).forEach(layer => layer.addTo(map));
        }}
        
        function clearAll() {{
            Object.values(layers).forEach(layer => map.removeLayer(layer));
        }}
        
        // 初始化
        createLayers();
        showLevel(1); // 默认显示Level 1
    </script>
</body>
</html>
"""
    
    return html_content


def main():
    """主函数"""
    print("🚀 开始H3扫描功能演示...")
    
    try:
        # 创建演示数据
        demo_data = create_scanning_demo()
        
        # 生成HTML
        html_content = generate_scanning_html(demo_data)
        
        # 保存文件
        output_file = "h3_scanning_demo.html"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ 扫描演示文件已生成: {output_file}")
        print("\n📊 演示数据概览:")
        
        for level_data in demo_data["scanning_demo"]:
            level = level_data["level"]
            total_places = sum(r["places_count"] for r in level_data["results"])
            drill_down_count = sum(1 for r in level_data["results"] if r["should_drill_down"])
            
            print(f"  Level {level}: 分辨率{level_data['h3_resolution']}, 半径{level_data['search_radius']}m")
            print(f"    扫描{level_data['grid_count']}个网格, 找到{total_places}个地点, {drill_down_count}个需要钻入")
        
        print(f"\n🌐 请在浏览器中打开 {output_file} 查看扫描演示")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
