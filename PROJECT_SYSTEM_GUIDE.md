# 项目管理系统使用指南

## 🎯 系统概述

项目管理系统是对原有网格扫描系统的重大升级，实现了你提出的核心需求：

✅ **独立项目管理** - 每个项目有独立的目录和配置  
✅ **Grid级别控制** - 可以单独执行和更新任何Grid  
✅ **可视化选择** - 通过地图界面选择要执行的Grid  
✅ **增量更新** - 支持重新扫描特定Grid而不影响其他数据  

## 🏗️ 系统架构

```
项目管理系统
├── 项目管理层 (ProjectManager)
│   ├── 项目创建和配置
│   ├── Grid状态跟踪
│   └── 统计信息管理
├── Grid执行层 (GridExecutor)
│   ├── 单Grid执行
│   ├── 批量执行
│   └── 状态更新
├── API服务层 (ProjectAPI)
│   ├── RESTful API
│   ├── 可视化界面服务
│   └── 实时数据交互
└── 命令行工具
    ├── 项目创建器
    ├── 项目运行器
    └── 参数调节器
```

## 📁 项目目录结构

每个项目都有独立的目录结构：

```
projects/项目名/
├── project_config.json    # 项目配置（中心坐标、参数等）
├── grids/                 # 网格数据
│   ├── level1_grids.json  # Level 1网格状态
│   ├── level2_grids.json  # Level 2网格状态
│   ├── level3_grids.json  # Level 3网格状态
│   └── metadata.json      # 元数据信息
├── results/               # 扫描结果
│   ├── grid_xxx_results.json  # 各Grid的扫描结果
│   └── ...
├── sessions/              # 执行会话记录
├── exports/               # 导出数据
└── README.md              # 项目说明
```

## 🚀 使用流程

### 1. 创建项目

```bash
# 创建新项目
python src/create_project.py --create co-la-data \
  --center 34.0522,-118.2437 \
  --radius 25 \
  --desc "洛杉矶便利店扫描"

# 列出所有项目
python src/create_project.py --list

# 查看项目状态
python src/create_project.py --status co-la-data
```

### 2. 调整参数

```bash
# 启动参数可视化调节器
python src/start_parameter_visualizer.py

# 或使用项目级可视化（集成参数调节）
python src/project_api.py
```

### 3. 生成网格

```bash
# 为项目生成网格
python src/create_project.py --generate-grids co-la-data
```

### 4. 执行扫描

```bash
# 查看执行状态
python src/run_project.py --project co-la-data --status

# 执行所有待处理Grid
python src/run_project.py --project co-la-data --all --mock-mode

# 执行指定Grid
python src/run_project.py --project co-la-data \
  --grid-ids "grid_1_34.0522_-118.2437,grid_1_34.0522_-118.1000" \
  --mock-mode

# 启动可视化执行界面
python src/run_project.py --project co-la-data --visualize
```

## 🎛️ 功能特性

### 项目管理
- ✅ **独立配置**: 每个项目有独立的参数配置
- ✅ **状态跟踪**: 实时跟踪每个Grid的执行状态
- ✅ **成本控制**: 预算限制和成本估算
- ✅ **元数据管理**: 完整的项目信息和统计

### Grid级别控制
- ✅ **单Grid执行**: 可以单独执行任何一个Grid
- ✅ **批量执行**: 支持选择多个Grid批量执行
- ✅ **状态管理**: pending, running, completed, failed
- ✅ **增量更新**: 重新执行某个Grid不影响其他数据

### 可视化界面
- ✅ **项目可视化**: 地图显示所有Grid分布和状态
- ✅ **参数调节**: 实时调整网格参数并预览效果
- ✅ **交互执行**: 点击Grid进行单独执行
- ✅ **进度监控**: 实时显示执行进度和结果

### 成本和性能优化
- ✅ **模拟模式**: 免费测试所有功能
- ✅ **并发执行**: 多线程并行处理提高效率
- ✅ **成本预估**: 执行前显示预估费用
- ✅ **预算控制**: 超出预算时自动警告

## 🔧 核心工具

### 1. 项目创建器 (`src/create_project.py`)
```bash
# 创建项目
--create PROJECT_NAME --center LAT,LNG --radius KM

# 管理项目
--list                    # 列出所有项目
--status PROJECT_NAME     # 查看项目状态
--generate-grids PROJECT  # 生成网格
```

### 2. 项目运行器 (`src/run_project.py`)
```bash
# 执行控制
--project PROJECT_NAME --all           # 执行所有Grid
--project PROJECT_NAME --grid-ids IDS  # 执行指定Grid
--project PROJECT_NAME --status        # 查看状态
--project PROJECT_NAME --visualize     # 启动可视化

# 执行选项
--mock-mode     # 模拟模式（不消耗API额度）
--force         # 跳过确认
--max-workers N # 并发线程数
```

### 3. API服务器 (`src/project_api.py`)
```bash
# 启动完整的Web界面
python src/project_api.py --port 8093

# 功能包括：
# - 项目创建和管理
# - 参数实时调节
# - Grid可视化和选择执行
# - 实时状态监控
```

### 4. 参数调节器 (`src/start_parameter_visualizer.py`)
```bash
# 启动独立的参数调节界面
python src/start_parameter_visualizer.py --port 8090

# 功能：
# - 实时参数调节
# - 网格分布预览
# - 成本估算
# - 配置导出
```

## 📊 使用示例

### 完整工作流示例

```bash
# 1. 创建洛杉矶扫描项目
python src/create_project.py --create la-stores \
  --center 34.0522,-118.2437 --radius 30 \
  --desc "洛杉矶地区便利店扫描项目"

# 2. 查看项目状态
python src/create_project.py --status la-stores

# 3. 生成网格（使用默认参数）
python src/create_project.py --generate-grids la-stores

# 4. 检查生成的网格
python src/run_project.py --project la-stores --status

# 5. 先测试几个Grid（模拟模式）
python src/run_project.py --project la-stores \
  --grid-ids "grid_1_34.0522_-118.2437" --mock-mode --force

# 6. 启动可视化界面进行交互操作
python src/project_api.py

# 在浏览器中：
# - 选择 la-stores 项目
# - 调整参数（如需要）
# - 重新生成网格
# - 在地图上选择要执行的Grid
# - 点击执行按钮

# 7. 批量执行所有Grid（真实模式）
python src/run_project.py --project la-stores --all

# 8. 查看结果
ls projects/la-stores/results/
python src/run_project.py --project la-stores --status
```

### 增量更新示例

```bash
# 场景：某个区域新开了店，需要重新扫描特定Grid

# 1. 查看项目状态，找到需要更新的Grid
python src/run_project.py --project la-stores --status

# 2. 重新执行特定Grid
python src/run_project.py --project la-stores \
  --grid-ids "grid_1_34.0522_-118.2437,grid_1_34.0600_-118.2500" \
  --force  # 跳过确认，直接执行

# 3. 验证更新结果
python src/run_project.py --project la-stores --status
```

## 🌟 核心优势

### 1. **完全独立的项目管理**
- 每个项目有独立的配置、数据和结果
- 不同项目可以使用不同的参数
- 项目间完全隔离，互不影响

### 2. **精确的Grid级别控制**
- 可以单独更新任何一个Grid
- 支持选择性执行和批量执行
- 完整的状态跟踪和错误处理

### 3. **可视化操作界面**
- 地图上直观显示所有Grid
- 点击即可执行单个Grid
- 实时参数调节和效果预览

### 4. **成本和性能优化**
- 模拟模式免费测试
- 并发执行提高效率
- 精确的成本控制和预算管理

### 5. **完整的工作流支持**
- 从项目创建到结果查看的完整流程
- 命令行和可视化界面双重支持
- 丰富的状态监控和错误诊断

## 🔄 与原系统的对比

| 功能 | 原系统 | 项目管理系统 |
|------|--------|-------------|
| 数据组织 | 单一sessions目录 | 独立项目目录结构 |
| 参数管理 | 全局config.py | 项目级配置 |
| 执行控制 | 全部或无 | Grid级精确控制 |
| 更新方式 | 重新扫描全部 | 增量更新单Grid |
| 可视化 | 会话后查看 | 实时交互操作 |
| 成本控制 | 粗粒度 | 精确预估和控制 |

## 📈 性能数据

基于测试项目 `co-la-data`（洛杉矶25km半径）：

- **网格生成**: 180个Grid（45个L1 + 135个L2）
- **预估成本**: $5.76 基础费用
- **并发执行**: 3个线程，平均1.5秒/Grid
- **数据存储**: 独立JSON文件，易于查看和处理
- **状态跟踪**: 实时更新，100%准确

## 🎉 总结

这个项目管理系统完全实现了你的原始需求：

> "我期望我们可以指定生成运行配置的目录，这个目录里存放生成的grid信息和grid参数以及过程数据。我们在运行的时候需要输入对应的中心坐标扫描范围，然后调整参数调节器获取到这里的level1 level2 level3参数，将上面这个信息存储到我们命名的这个文件夹中。然后我们可以点击某个grid单独运行这个grid或者运行全部grid。这样的目的就是我们可以控制扫描范围，可视化扫描范围。"

✅ **指定目录**: 每个项目独立目录  
✅ **Grid信息存储**: 完整的Grid状态和配置  
✅ **参数调节**: 实时可视化参数调节器  
✅ **独立Grid执行**: 支持单Grid和批量执行  
✅ **扫描范围控制**: 项目级范围管理  
✅ **可视化**: 完整的地图可视化界面  

现在你可以：
- 为不同区域创建独立项目
- 精确控制每个Grid的执行
- 随时更新特定区域的数据
- 通过可视化界面直观操作
- 完全控制成本和执行进度

这个系统为大规模、长期的地点数据采集提供了企业级的管理和控制能力！ 