#!/usr/bin/env python3
"""
测试 core_code/adaptive_grid_scan.py 中新添加的 run_single_grid 方法
"""

import sys
import os

# 添加 core_code 目录到 Python 路径
core_code_path = os.path.join(os.path.dirname(__file__), 'core_code')
sys.path.insert(0, core_code_path)

import h3
from adaptive_grid_scan import AdaptiveScanner

def test_run_single_grid():
    """测试单网格扫描功能"""
    print("🧪 测试 run_single_grid 方法...")
    
    # 创建扫描器（使用mock模式）
    scanner = AdaptiveScanner(mock=True)
    
    # 测试参数
    # 使用洛杉矶市中心的一个H3网格
    center_lat, center_lng = 34.0522, -118.2437
    h3_res = 5
    grid_id = h3.latlng_to_cell(center_lat, center_lng, h3_res)
    
    level = 1
    place_types = ["convenience_store"]
    layer_config = [
        {"h3_res": 5, "search_radius": 5000},   # Level 1
        {"h3_res": 7, "search_radius": 1000},   # Level 2
        {"h3_res": 8, "search_radius": 500},    # Level 3
    ]
    
    print(f"测试网格: {grid_id}")
    print(f"网格中心: ({center_lat}, {center_lng})")
    print(f"扫描层级: {level}")
    print(f"地点类型: {place_types}")
    
    try:
        # 执行单网格扫描
        result = scanner.run_single_grid(grid_id, level, place_types, layer_config)
        
        # 验证结果结构
        assert "places" in result, "结果中缺少 'places' 字段"
        assert "should_drill_down" in result, "结果中缺少 'should_drill_down' 字段"
        assert "metadata" in result, "结果中缺少 'metadata' 字段"
        
        # 验证metadata结构
        metadata = result["metadata"]
        required_metadata_fields = [
            "api_calls", "scan_time", "grid_center", "search_radius",
            "h3_resolution", "level", "place_types", "execution_time_seconds"
        ]
        for field in required_metadata_fields:
            assert field in metadata, f"metadata中缺少 '{field}' 字段"
        
        # 打印结果
        print("\n✅ 测试成功！")
        print(f"找到地点数量: {len(result['places'])}")
        print(f"是否需要钻入下一层: {result['should_drill_down']}")
        print(f"API调用次数: {metadata['api_calls']}")
        print(f"执行时间: {metadata['execution_time_seconds']:.3f}秒")
        print(f"搜索半径: {metadata['search_radius']}米")
        print(f"H3分辨率: {metadata['h3_resolution']}")
        
        # 显示前几个地点作为示例
        if result['places']:
            print(f"\n前3个地点示例:")
            for i, place in enumerate(result['places'][:3]):
                print(f"  {i+1}. {place.get('name', 'Unknown')} - {place.get('id', 'No ID')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_cases():
    """测试错误情况"""
    print("\n🧪 测试错误情况...")
    
    scanner = AdaptiveScanner(mock=True)
    layer_config = [{"h3_res": 5, "search_radius": 5000}]
    
    # 测试无效的grid_id
    try:
        scanner.run_single_grid("invalid_grid_id", 1, ["convenience_store"], layer_config)
        print("❌ 应该抛出无效grid_id错误")
        return False
    except ValueError as e:
        print(f"✅ 正确捕获无效grid_id错误: {e}")
    
    # 测试无效的level
    try:
        valid_grid = h3.latlng_to_cell(34.0522, -118.2437, 5)
        scanner.run_single_grid(valid_grid, 5, ["convenience_store"], layer_config)
        print("❌ 应该抛出无效level错误")
        return False
    except ValueError as e:
        print(f"✅ 正确捕获无效level错误: {e}")
    
    # 测试空的place_types
    try:
        valid_grid = h3.latlng_to_cell(34.0522, -118.2437, 5)
        scanner.run_single_grid(valid_grid, 1, [], layer_config)
        print("❌ 应该抛出空place_types错误")
        return False
    except ValueError as e:
        print(f"✅ 正确捕获空place_types错误: {e}")
    
    return True

if __name__ == "__main__":
    print("🚀 开始测试 run_single_grid 方法...")
    
    success1 = test_run_single_grid()
    success2 = test_error_cases()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！")
        sys.exit(0)
    else:
        print("\n💥 测试失败！")
        sys.exit(1)
