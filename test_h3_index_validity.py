#!/usr/bin/env python3
"""
验证H3索引有效性
确认生成的H3索引是否有效且正确
"""

import h3
import json
from pathlib import Path

def test_h3_index_validity():
    """测试H3索引有效性"""
    
    print("🔍 验证H3索引有效性")
    print("=" * 60)
    
    # 测试基本H3索引有效性
    print("\n1. 基本H3索引有效性测试:")
    print("-" * 40)
    
    test_coordinates = [
        (34.0522, -118.2437),  # Los Angeles
        (40.7128, -74.0060),   # New York
        (51.5074, -0.1278),    # London
        (35.6762, 139.6503),   # Tokyo
    ]
    
    for lat, lng in test_coordinates:
        print(f"\n坐标: ({lat}, {lng})")
        
        for res in [5, 6, 7, 8, 9]:
            try:
                h3_index = h3.latlng_to_cell(lat, lng, res)
                
                # 验证索引有效性
                is_valid = h3.is_valid_cell(h3_index)
                resolution = h3.get_resolution(h3_index)
                
                print(f"  分辨率 {res}: {h3_index}")
                print(f"    有效性: {'✅' if is_valid else '❌'}")
                print(f"    分辨率验证: {'✅' if resolution == res else '❌'} (期望: {res}, 实际: {resolution})")
                
                if is_valid:
                    # 测试反向转换
                    recovered_lat, recovered_lng = h3.cell_to_latlng(h3_index)
                    distance = ((lat - recovered_lat)**2 + (lng - recovered_lng)**2)**0.5
                    print(f"    反向转换: ({recovered_lat:.6f}, {recovered_lng:.6f})")
                    print(f"    距离误差: {distance:.8f}°")
                
            except Exception as e:
                print(f"  分辨率 {res}: ❌ 生成失败 - {e}")
    
    # 测试项目中的H3索引
    print(f"\n2. 项目H3索引验证:")
    print("-" * 40)
    
    test_projects = ["test-strong-overlap", "test-correct-radius"]
    
    for project_name in test_projects:
        print(f"\n项目: {project_name}")
        
        # 检查各级别的网格文件
        project_dir = Path("projects") / project_name
        grids_dir = project_dir / "grids"
        
        if not grids_dir.exists():
            print(f"  ❌ 网格目录不存在")
            continue
        
        # 检查配置文件
        config_file = project_dir / "project_config.json"
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            expected_resolutions = {
                f"level{config['h3_res_level1']}_grids.json": config['h3_res_level1'],
                f"level{config['h3_res_level2']}_grids.json": config['h3_res_level2'],
                f"level{config['h3_res_level3']}_grids.json": config['h3_res_level3'],
            }
        else:
            expected_resolutions = {
                "level5_grids.json": 5,
                "level7_grids.json": 7,
                "level8_grids.json": 8,
            }
        
        for grid_file, expected_res in expected_resolutions.items():
            grid_path = grids_dir / grid_file
            
            if not grid_path.exists():
                print(f"  {grid_file}: 不存在")
                continue
            
            try:
                with open(grid_path, 'r', encoding='utf-8') as f:
                    grids = json.load(f)
                
                print(f"  {grid_file}: {len(grids)} 个网格")
                
                valid_count = 0
                invalid_count = 0
                resolution_mismatch = 0
                
                for i, grid in enumerate(grids):
                    h3_index = grid.get('h3_index', '')
                    
                    if not h3_index:
                        invalid_count += 1
                        print(f"    ❌ 网格 {i+1}: 缺少h3_index")
                        continue
                    
                    # 验证索引有效性
                    try:
                        is_valid = h3.is_valid_cell(h3_index)
                        
                        if not is_valid:
                            invalid_count += 1
                            print(f"    ❌ 网格 {i+1}: 无效H3索引 {h3_index}")
                            continue
                        
                        # 验证分辨率
                        actual_res = h3.get_resolution(h3_index)
                        if actual_res != expected_res:
                            resolution_mismatch += 1
                            print(f"    ⚠️  网格 {i+1}: 分辨率不匹配 (期望: {expected_res}, 实际: {actual_res})")
                        
                        # 验证坐标一致性
                        stored_lat = grid.get('latitude', 0)
                        stored_lng = grid.get('longitude', 0)
                        
                        if stored_lat and stored_lng:
                            h3_lat, h3_lng = h3.cell_to_latlng(h3_index)
                            lat_diff = abs(stored_lat - h3_lat)
                            lng_diff = abs(stored_lng - h3_lng)
                            
                            if lat_diff > 1e-6 or lng_diff > 1e-6:
                                print(f"    ⚠️  网格 {i+1}: 坐标不一致")
                                print(f"        存储: ({stored_lat:.6f}, {stored_lng:.6f})")
                                print(f"        H3计算: ({h3_lat:.6f}, {h3_lng:.6f})")
                        
                        valid_count += 1
                        
                    except Exception as e:
                        invalid_count += 1
                        print(f"    ❌ 网格 {i+1}: 验证失败 - {e}")
                
                print(f"    总结: {valid_count} 个有效, {invalid_count} 个无效, {resolution_mismatch} 个分辨率不匹配")
                
            except Exception as e:
                print(f"  ❌ {grid_file}: 读取失败 - {e}")
    
    # 测试子网格索引有效性
    print(f"\n3. 子网格索引有效性测试:")
    print("-" * 40)
    
    parent_h3 = h3.latlng_to_cell(34.0522, -118.2437, 5)
    print(f"父网格 (res 5): {parent_h3}")
    print(f"父网格有效性: {'✅' if h3.is_valid_cell(parent_h3) else '❌'}")
    
    # 测试不同级别的子网格
    for child_res in [6, 7, 8]:
        try:
            child_indexes = h3.cell_to_children(parent_h3, child_res)
            print(f"\n子网格 (res {child_res}): {len(child_indexes)} 个")
            
            valid_children = 0
            invalid_children = 0
            
            for i, child_h3 in enumerate(list(child_indexes)[:10]):  # 测试前10个
                try:
                    is_valid = h3.is_valid_cell(child_h3)
                    actual_res = h3.get_resolution(child_h3)
                    
                    if is_valid and actual_res == child_res:
                        valid_children += 1
                    else:
                        invalid_children += 1
                        print(f"  ❌ 子网格 {i+1}: {child_h3}")
                        print(f"      有效性: {is_valid}, 分辨率: {actual_res} (期望: {child_res})")
                        
                except Exception as e:
                    invalid_children += 1
                    print(f"  ❌ 子网格 {i+1}: 验证失败 - {e}")
            
            print(f"  验证结果: {valid_children} 个有效, {invalid_children} 个无效")
            
        except Exception as e:
            print(f"  ❌ 生成res {child_res}子网格失败: {e}")
    
    # 测试边界情况
    print(f"\n4. 边界情况测试:")
    print("-" * 40)
    
    edge_cases = [
        ("极地附近", 89.0, 0.0),
        ("日期变更线", 0.0, 179.0),
        ("南极附近", -89.0, 0.0),
        ("反日期变更线", 0.0, -179.0),
    ]
    
    for case_name, lat, lng in edge_cases:
        print(f"\n{case_name}: ({lat}, {lng})")
        
        try:
            h3_index = h3.latlng_to_cell(lat, lng, 5)
            is_valid = h3.is_valid_cell(h3_index)
            
            print(f"  H3索引: {h3_index}")
            print(f"  有效性: {'✅' if is_valid else '❌'}")
            
            if is_valid:
                recovered_lat, recovered_lng = h3.cell_to_latlng(h3_index)
                print(f"  反向转换: ({recovered_lat:.6f}, {recovered_lng:.6f})")
                
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
    
    print(f"\n5. H3库信息:")
    print("-" * 40)
    print(f"H3库版本: {h3.__version__}")
    
    # 测试H3库的基本功能
    try:
        test_index = h3.latlng_to_cell(0, 0, 0)
        print(f"基本功能测试: ✅ 正常")
        print(f"全球根网格: {test_index}")
    except Exception as e:
        print(f"基本功能测试: ❌ 失败 - {e}")

if __name__ == "__main__":
    test_h3_index_validity()
