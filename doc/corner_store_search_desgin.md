# 基于高密度网格提取地址数据文档

## 0.背景

- 在进行商业选址、零售分析或区域覆盖评估时，尽可能获取完整的地区商铺信息，将显著提升我们在市场决策、资源配置与竞争分析方面的精度。

- 本方案目标是在确保数据完整性的前提下，尽可能降低 API 调用成本，构建一套可扩展、自动化、高效的地址数据采集机制。

## 1.数据采集

- **核心目标**: 在确保数据覆盖率的同时，尽可能地降低 API 调用成本，避免在商店稀疏的区域进行不必要的过度扫描。

- **核心 API**: **Google Places API (New) - `Nearby Search`**

  - **关键限制**: 新版`Nearby Search`每个请求**最多返回 20 个结果，且不支持分页**。我们的整个策略都是为了应对这个限制而设计的。

- **核心策略**: 基于网格切分进行多阶段数据采集。
  - 网格切分
    - ![alt text](<grid search.png>)
  - 数据提取
    - ![alt text](<google playgroud image.png>)
- **策略详解：自适应网格扫描 (Adaptive Grid Scanning)**

  - **步骤 1: 宏观区域扫描 (Macro-Level Reconnaissance Scan - 粗筛)**

    - **核心目标**: 以最低的成本快速识别出哪些广大区域是“商业荒漠”，从而避免在这些区域进行昂贵的精细扫描。
    - **网格参数**: 我们首先在整个 60 英里半径范围内，部署一个**非常稀疏**的网格。
      - **单次搜索半径**: **5 公里 (5,000 米)**
      - **网格中心点间距**: **7 公里**
    - **执行与判断**:
      - 对这个稀疏网格的每一个中心点发起一次`Nearby Search` API 调用。
      - **关键判断逻辑**:
        - 如果某次调用返回的结果数量**远低于 20**（例如，返回 0 个、1 个或少数几个结果），我们就认为这个**半径为 5 公里的巨大圆形区域**是低密度区，并**将其从后续的精细扫描计划中移除**。
        - 如果某次调用返回的结果数量**等于 20**，这恰恰证明了这是一个**极高密度的“热点”大区**，我们必须对其进行下一阶段的精细扫描。
    - **产出**: 一个被“标记”过的地图。大部分区域可能被标记为“低密度，跳过”，少数区域被标记为“高密度，需要详查”。

  - **步骤 2: 精细化覆盖扫描 (Fine-Grained Coverage Scan - 精筛)**

    - **核心目标**: 仅在第一步识别出的“高密度热点大区”内，执行我们原有的高密度网格扫描，以确保捕获所有商店。
    - **执行**:
      - 我们只在我们标记为“高密度，需要详查”的**5 公里半径圆形区域**内，生成我们之前规划的**1 公里半径/1.4 公里间距**的精细网格点。
      - 对这些新生成的、位于热点区域内的精细网格点，发起 API 调用。

  - **步骤 3: 极端密度区域增强扫描 (Hyper-Density Enhancement Scan)**
    - **核心目标**: 由于 API 最多只返回 20 个结果，因此 20 通常意味着‘结果被截断’，需要细化扫描。
    - **执行**:
      - 如果在**步骤 2（精细扫描）**中，有任何一次**1 公里半径**的调用返回了**正好 20 个结果**，我们就将该区域标记为“极端密度区”。
      - 对这些“极端密度区”，我们再进行一轮更小半径（例如**500 米半径/0.7 公里间距**）的最终扫描，确保数据无遗漏。

- **策略流程**

  ```mermaid
      flowchart TD
          A[起点：指定目标区域（60英里半径）] --> B[步骤 1：宏观区域扫描（Macro-Level Reconnaissance Scan - 粗筛）]
          B --> B1[构建稀疏网格（7公里间距）]
          B1 --> B2[每个中心点执行 Nearby Search（半径5公里）]
          B2 --> B3{返回结果数量 < 20？}
          B3 -- 是 --> C[标记为低密度区域，跳过后续扫描]
          B3 -- 否 --> D[标记为高密度热点大区]

          D --> E[步骤 2：精细化覆盖扫描（Fine-Grained Coverage Scan - 精筛）]
          E --> E1[构建精细网格（1.4公里间距，半径1公里）]
          E1 --> E2[每个精细网格点执行 Nearby Search]
          E2 --> E3{返回结果数量 = 20？}
          E3 -- 否 --> F[该区域已完成扫描]
          E3 -- 是 --> G[标记为极端密度区域（结果可能被截断）]

          G --> H[步骤 3：极端密度区域增强扫描（Hyper-Density Enhancement Scan）]
          H --> H1[构建更密集网格（0.7公里间距，半径500米）]
          H1 --> H2[再次执行 Nearby Search 扫描]
          H2 --> I[该区域完成扫描]

          C --> I
          F --> I
  ```

## 2. 成本估算

基于**自适应网格扫描**策略，提供一个针对洛杉矶地区的详细成本预测。

### 2.1 估算基础参数

- **计费模式**: `Nearby Search (New)` API 定价为 **$0.032 美元/次**。
- **总搜索区域**: 以洛杉矶市中心为原点，60 英里（约 96.6 公里）为半径的圆形区域，总面积 **29,315 平方公里**。

### 2.2 核心估算逻辑与假设

我们的估算基于对洛杉矶地区商业地理特征的综合判断，它既包含 DTLA、圣莫尼卡等高密度商业核心，也包含圣费尔南多谷等广阔的中低密度区域。

- **核心假设 1 (热点比例)**: 我们预测，经过粗筛后，洛杉矶地区约有 **20%** 的区域会被识别为“高密度热点大区”，需要进入步骤 2 的精筛。
- **核心假设 2 (极端密度比例)**: 我们预测，在精筛过程中，约有 **10%** 的调用会返回 20 个结果，表明存在商业高度集中的“极端密度区”，需要进入步骤 3 的增强扫描。

### 2.3 成本分解

- **步骤 1 (粗筛)**:

  - 固定调用数: 29,315 km² / (7km × 7km) ≈ **598 次**
  - 固定成本: 598 次 × $0.032 = **$19.14**

- **步骤 2 (精筛)**:

  - 需扫描面积: 29,315 km² × 20% = 5,863 km²
  - 调用次数: 5,863 km² / (1.4km × 1.4km) ≈ **2,991 次**
  - 成本: 2,991 次 × $0.032 = **$95.71**

- **步骤 3 (增强扫描)**:

  - 需增强的区域数: 2,991 次 × 10% ≈ 299 个
  - 每个区域需额外扫描约 4 次，调用次数: 299 × 4 ≈ **1,196 次**
  - 成本: 1,196 次 × $0.032 = **$38.27**

### 2.4 成本效益与结论

- **最终决策**: 本方案是在“成本”和“数据完整性”之间取得最佳平衡的专业执行路径。
- **成本效益**: 本方案预估调用 **4,785 次**（约$153），相比不使用本策略的“暴力扫描”（约 14,957 次，成本$478），**可节省约 68% 的 API 调用和费用**，效益显著。
- **注意事项**: 以上为基于专业假设的预测数据，而非最终实际发生的数据。最终成本受地点类型选择和真实商业密度影响。

## **附录**

- Google Places API: https://developers.google.com/maps/documentation/places/web-service/nearby-search
- Google Places Type: https://developers.google.com/maps/documentation/places/web-service/place-types
- Google Map API Price: https://developers.google.com/maps/billing-and-pricing/pricing
  - ![alt text](<google place nearby search.png>)
- Google Map Playgroude : https://places-search-405409.ue.r.appspot.com/

## **重要事项**

- **选择的地点类型是影响最终成本的关键变量**
- **利用免费额度,每月只扫描部分ZIPCODE区域是节省成本的关键**

## **需要确认**

- Response Field
  - places.id
  - places.photos
  - places.formattedAddress
  - places.location
  - places.name
  - places.postalAddress
  - places.types
  - [Optional]places.primaryType
  - [Optional]places.primaryTypeDisplayName

  - More view API documentation

- Include Places Type:
  - convenience_store
    - 专指便利店，是最准确匹配
  - grocery_store
    - 泛指小型杂货店，通常也可包含街角商店
  - food_store
    - 广义食品杂货店，可覆盖部分便利店功能
  - asian_grocery_store
    - 亚裔街角超市（如日式/韩式杂货铺）
  - liquor_store
    - 酒品为主的小商店，有时也卖零食
  - [Optional]market
    - 较为宽泛，有时可指小型集贸市场、社区型小店
  - [Optional]store
    - 泛指商店，可能用于便利店但不具特指性
  - **注意**: **选择的地点类型是影响最终成本的关键变量**。采用更窄的定义会使最终成本低于我们的基准预测；采用更宽泛的定义（如加入`store`, `market`）则会使成本高于预测。


## 会议结论

- 切合业务的类型
  - UMS Store Type 和包含 UMS store type 就可以
- 保留所以 Response
  - 尽量保留所有的 Response
- 半年刷新
  - 数据不会高频率刷新,基本半年一次
- PlaceId
  - 建议 12 月刷新一次
- 替换 ticket
  - 这个 sprint 开始抓取数据
- 确定一下哪些 metrics 可以做
  - 可以分析哪些 metrics