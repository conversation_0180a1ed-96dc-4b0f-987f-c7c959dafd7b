# Grid Map Scanner 重构任务清单

## 📋 项目概述

### 重构目标
将 `src/` 目录的复杂实现重构为基于 `core_code/` 的统一架构，保持项目管理和可视化功能的同时，确保核心扫描逻辑的一致性和可靠性。

### 核心原则
- **职责分离**: 扫描逻辑 vs 决策逻辑 vs 状态管理完全分离
- **最小修改**: 对 `core_code/` 的修改最小化，保持原有功能不变
- **配置统一**: 项目级配置，动态传递给核心引擎
- **无状态服务**: H3 Service Layer 提供纯功能性服务
- **简单可靠**: 优先保证功能正确性，避免过度复杂化

## 🏗️ 目标架构

```
┌─────────────────────────────────────┐
│  Web UI & Visualization             │  ← 需要快速H3预览
├─────────────────────────────────────┤
│  Project Management                 │  ← 项目管理、状态跟踪、决策控制
├─────────────────────────────────────┤
│  H3 Service Layer (新增)            │  ← 统一的H3服务层
│  ├── H3 Converter (基础转换)        │  ← 坐标↔H3转换
│  ├── H3 Visualizer (可视化支持)     │  ← 专门支持可视化
│  └── H3 Scanner Bridge (扫描桥接)   │  ← 连接core_code
├─────────────────────────────────────┤
│  Core Scanning Engine              │  ← core_code (保持不变+小幅扩展)
└─────────────────────────────────────┘
```

## 🔧 组件设计详情

### Core Scanning Engine (core_code/)
**保持功能**:
- `AdaptiveScanner.run()` - 原有区域扫描功能
- `PlacesClient` - 原有API客户端功能

**新增功能**:
```python
class AdaptiveScanner:
    def run_single_grid(self, grid_id: str, level: int, place_types: List[str], layer_config: List[dict]) -> dict:
        """
        单网格扫描方法
        Args:
            grid_id: H3网格ID
            level: 扫描层级 (1, 2, 3)
            place_types: 地点类型列表
            layer_config: 层级配置 [{"h3_res": 5, "search_radius": 5000}, ...]
        Returns:
            {
                "places": [...],           # 找到的地点列表
                "should_drill_down": bool, # 是否需要钻入下一层 (len(places) >= 20)
                "metadata": {              # 扫描元数据
                    "api_calls": 1,
                    "scan_time": "2024-01-01T12:00:00",
                    "grid_center": [lat, lon]
                }
            }
        """
```

### H3 Service Layer (新增)

#### H3 Converter (基础转换)
```python
class H3Converter:
    @staticmethod
    def area_to_grids(center_lat: float, center_lng: float, radius_km: float, h3_resolution: int) -> List[str]:
        """区域转H3网格列表"""

    @staticmethod
    def grid_to_center(h3_id: str) -> Tuple[float, float]:
        """H3网格ID转中心坐标"""

    @staticmethod
    def generate_children(parent_h3_id: str, child_resolution: int) -> List[str]:
        """生成子网格"""

    @staticmethod
    def get_grid_info(h3_id: str) -> dict:
        """获取网格信息 (分辨率、大小等)"""
```

#### H3 Visualizer (可视化支持)
```python
class H3Visualizer:
    @staticmethod
    def get_grid_boundary(h3_id: str) -> List[Tuple[float, float]]:
        """获取网格边界坐标 (用于地图绘制)"""

    @staticmethod
    def get_grid_coverage_info(h3_id: str) -> dict:
        """获取网格覆盖范围信息"""
```

#### H3 Scanner Bridge (扫描桥接)
```python
class H3ScannerBridge:
    def __init__(self, project_config: ScanConfig):
        """使用项目级配置初始化"""

    def scan_single_grid(self, grid_id: str, level: int) -> GridScanResult:
        """
        单网格扫描
        1. 配置格式转换 (ScanConfig → core_code格式)
        2. 调用 core_code.AdaptiveScanner.run_single_grid()
        3. 结果格式转换 (core_code → GridScanResult)
        """

    def scan_multiple_grids(self, grid_specs: List[GridSpec]) -> List[GridScanResult]:
        """批量网格扫描 (内部循环调用单网格扫描)"""
```

### Project Management (保持+简化)
**保持功能**:
- 项目创建和管理
- 网格状态跟踪
- 配置管理
- 结果存储

**修改功能**:
- `GridExecutor` 改为调用 `H3ScannerBridge`
- 简化 `PlacesClient` 为适配器
- 删除重复的 `adaptive_grid_scan.py`

## 📊 数据流设计

### 单网格扫描流程
```
1. Project Management 调用: scan_grid(grid_id, level)
2. H3 Scanner Bridge:
   - 使用项目配置
   - 配置格式转换
   - 调用 core_code.run_single_grid()
3. Core Code:
   - 执行单网格扫描
   - 返回: {places, should_drill_down, metadata}
4. H3 Scanner Bridge:
   - 结果格式转换
   - 返回 GridScanResult
5. Project Management:
   - 保存扫描结果
   - 根据 should_drill_down 决定是否生成子网格
   - 更新网格状态
```

### 批量网格扫描流程
```
1. Project Management 调用: scan_grids([grid_id1, grid_id2, ...])
2. H3 Scanner Bridge 循环处理每个网格
3. 返回所有网格的独立结果
```

## 🎯 重构实施计划

### 阶段1: 核心基础设施 (优先级: 高)
- [ ] **1.1** 修改 `core_code/adaptive_grid_scan.py`
  - [ ] 添加 `run_single_grid` 方法
  - [ ] 支持动态配置参数传递
  - [ ] 测试单网格扫描功能

- [ ] **1.2** 创建 `src/h3_service/h3_converter.py`
  - [ ] 实现基础坐标转换功能
  - [ ] 实现区域到网格转换
  - [ ] 实现父子网格关系查询
  - [ ] 单元测试覆盖

- [ ] **1.3** 创建 `src/h3_service/h3_scanner_bridge.py`
  - [ ] 实现配置格式转换
  - [ ] 实现单网格扫描包装
  - [ ] 实现结果格式转换
  - [ ] 集成测试

### 阶段2: 项目管理集成 (优先级: 高)
- [ ] **2.1** 重构 `src/grid_executor.py`
  - [ ] 移除重复的扫描逻辑
  - [ ] 集成 H3ScannerBridge
  - [ ] 保持原有接口不变
  - [ ] 功能测试验证

- [ ] **2.2** 简化 `src/places_client.py`
  - [ ] 移除重复实现
  - [ ] 改为适配器模式
  - [ ] 确保向后兼容

- [ ] **2.3** 清理重复文件
  - [ ] 删除 `src/adaptive_grid_scan.py`
  - [ ] 更新相关导入引用
  - [ ] 验证无破坏性影响

### 阶段3: 可视化支持 (优先级: 中)
- [ ] **3.1** 创建 `src/h3_service/h3_visualizer.py`
  - [ ] 实现网格边界计算
  - [ ] 实现网格覆盖信息
  - [ ] 支持地图可视化需求

- [ ] **3.2** 更新 Web UI 集成
  - [ ] 确保可视化界面正常工作
  - [ ] 验证网格显示功能
  - [ ] 测试交互功能

### 阶段4: 测试和优化 (优先级: 中)
- [ ] **4.1** 端到端测试
  - [ ] 完整项目创建到执行流程测试
  - [ ] Mock模式和真实API模式测试
  - [ ] 性能基准测试

- [ ] **4.2** 文档更新
  - [ ] 更新 README.md
  - [ ] 更新 API 文档
  - [ ] 添加架构说明

- [ ] **4.3** 代码优化
  - [ ] 代码审查和重构
  - [ ] 性能优化
  - [ ] 错误处理完善

## 📋 验收标准

### 功能验收
- [ ] 所有原有功能正常工作
- [ ] 项目创建、网格生成、扫描执行流程完整
- [ ] Web 可视化界面正常显示
- [ ] Mock 模式和真实 API 模式都能正常工作

### 架构验收
- [ ] 核心扫描逻辑统一使用 `core_code/`
- [ ] 无重复实现的代码
- [ ] 职责分离清晰
- [ ] 配置管理统一

### 质量验收
- [ ] 单元测试覆盖率 > 80%
- [ ] 集成测试通过
- [ ] 性能不低于重构前
- [ ] 代码可维护性提升

## 🚨 风险和注意事项

### 技术风险
- **配置兼容性**: 确保新旧配置格式的兼容性
- **数据格式**: 注意数据格式转换的正确性
- **性能影响**: 监控重构后的性能变化

### 实施风险
- **功能回归**: 每个阶段都要进行充分测试
- **接口变更**: 尽量保持对外接口的稳定性
- **依赖管理**: 注意模块间依赖关系的变化

### 缓解措施
- 分阶段实施，每阶段都有明确的验收标准
- 保持原有代码备份，支持快速回滚
- 充分的测试覆盖，包括单元测试和集成测试

## 📝 进度跟踪

### 当前状态: 设计完成 ✅
- [x] 架构设计确认
- [x] 实施计划制定
- [x] 验收标准定义

### 下一步行动
1. 开始阶段1.1: 修改 `core_code/adaptive_grid_scan.py`
2. 准备开发环境和测试框架
3. 创建功能分支进行开发

---

**文档版本**: v1.0
**创建日期**: 2024-12-19
**最后更新**: 2024-12-19
**状态**: 设计完成，待实施

## 🎯 重构实施计划

### 阶段1: 核心基础设施 (优先级: 高)
- [ ] **1.1** 修改 `core_code/adaptive_grid_scan.py`
  - [ ] 添加 `run_single_grid` 方法
  - [ ] 支持动态配置参数传递
  - [ ] 测试单网格扫描功能

- [ ] **1.2** 创建 `src/h3_service/h3_converter.py`
  - [ ] 实现基础坐标转换功能
  - [ ] 实现区域到网格转换
  - [ ] 实现父子网格关系查询
  - [ ] 单元测试覆盖

- [ ] **1.3** 创建 `src/h3_service/h3_scanner_bridge.py`
  - [ ] 实现配置格式转换
  - [ ] 实现单网格扫描包装
  - [ ] 实现结果格式转换
  - [ ] 集成测试

### 阶段2: 项目管理集成 (优先级: 高)
- [ ] **2.1** 重构 `src/grid_executor.py`
  - [ ] 移除重复的扫描逻辑
  - [ ] 集成 H3ScannerBridge
  - [ ] 保持原有接口不变
  - [ ] 功能测试验证

- [ ] **2.2** 简化 `src/places_client.py`
  - [ ] 移除重复实现
  - [ ] 改为适配器模式
  - [ ] 确保向后兼容

- [ ] **2.3** 清理重复文件
  - [ ] 删除 `src/adaptive_grid_scan.py`
  - [ ] 更新相关导入引用
  - [ ] 验证无破坏性影响

### 阶段3: 可视化支持 (优先级: 中)
- [ ] **3.1** 创建 `src/h3_service/h3_visualizer.py`
  - [ ] 实现网格边界计算
  - [ ] 实现网格覆盖信息
  - [ ] 支持地图可视化需求

- [ ] **3.2** 更新 Web UI 集成
  - [ ] 确保可视化界面正常工作
  - [ ] 验证网格显示功能
  - [ ] 测试交互功能

### 阶段4: 测试和优化 (优先级: 中)
- [ ] **4.1** 端到端测试
  - [ ] 完整项目创建到执行流程测试
  - [ ] Mock模式和真实API模式测试
  - [ ] 性能基准测试

- [ ] **4.2** 文档更新
  - [ ] 更新 README.md
  - [ ] 更新 API 文档
  - [ ] 添加架构说明

- [ ] **4.3** 代码优化
  - [ ] 代码审查和重构
  - [ ] 性能优化
  - [ ] 错误处理完善