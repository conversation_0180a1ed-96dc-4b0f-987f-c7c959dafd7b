#!/usr/bin/env python3
"""
测试网格边界绘制逻辑
验证H3网格边界是否正确绘制
"""

import h3
import json
from pathlib import Path

def test_grid_boundary_drawing():
    """测试网格边界绘制"""
    
    print("🔍 测试网格边界绘制逻辑")
    print("=" * 60)
    
    # 测试H3边界获取
    print("\n1. H3边界获取测试:")
    print("-" * 40)
    
    test_lat, test_lng = 34.0522, -118.2437
    
    for res in [5, 6, 7, 8]:
        h3_index = h3.latlng_to_cell(test_lat, test_lng, res)
        boundary = h3.cell_to_boundary(h3_index)
        
        print(f"分辨率 {res}:")
        print(f"  H3索引: {h3_index}")
        print(f"  边界点数: {len(boundary)}")
        
        if len(boundary) == 6:
            print(f"  ✅ 正确的六边形")
            # 显示边界点
            print(f"  边界点:")
            for i, (lat, lng) in enumerate(boundary):
                print(f"    {i+1}. ({lat:.6f}, {lng:.6f})")
        else:
            print(f"  ❌ 异常形状: {len(boundary)} 个点")
        print()
    
    # 测试实际项目中的边界
    print("\n2. 实际项目边界测试:")
    print("-" * 40)
    
    test_projects = ["test-strong-overlap"]
    
    for project_name in test_projects:
        grids_file = Path("projects") / project_name / "grids" / "level5_grids.json"
        
        if not grids_file.exists():
            print(f"❌ 项目 {project_name} 网格文件不存在")
            continue
            
        print(f"\n项目: {project_name}")
        
        # 加载Level 5网格
        with open(grids_file, 'r', encoding='utf-8') as f:
            level5_grids = json.load(f)
        
        print(f"  Level 5网格数量: {len(level5_grids)}")
        
        # 测试每个网格的边界
        normal_count = 0
        abnormal_count = 0
        
        for i, grid in enumerate(level5_grids):
            h3_index = grid['h3_index']
            
            try:
                boundary = h3.cell_to_boundary(h3_index)
                
                if len(boundary) == 6:
                    normal_count += 1
                    if i < 3:  # 只显示前3个的详细信息
                        print(f"  网格 {i+1} ({h3_index}):")
                        print(f"    中心: ({grid['latitude']:.6f}, {grid['longitude']:.6f})")
                        print(f"    边界点数: {len(boundary)} ✅")
                else:
                    abnormal_count += 1
                    print(f"  ❌ 网格 {i+1} ({h3_index}) 边界异常: {len(boundary)} 个点")
                    
            except Exception as e:
                abnormal_count += 1
                print(f"  ❌ 网格 {i+1} ({h3_index}) 边界获取失败: {e}")
        
        print(f"  总结: {normal_count} 个正常, {abnormal_count} 个异常")
    
    # 测试子网格边界
    print("\n3. 子网格边界测试:")
    print("-" * 40)
    
    # 使用一个Level 5网格生成Level 7子网格
    parent_h3 = h3.latlng_to_cell(34.0522, -118.2437, 5)
    child_h3_indexes = h3.cell_to_children(parent_h3, 7)
    
    print(f"父网格 (res 5): {parent_h3}")
    print(f"子网格数量 (res 7): {len(child_h3_indexes)}")
    
    normal_children = 0
    abnormal_children = 0
    
    for i, child_h3 in enumerate(list(child_h3_indexes)[:10]):  # 测试前10个
        try:
            boundary = h3.cell_to_boundary(child_h3)
            
            if len(boundary) == 6:
                normal_children += 1
                if i < 3:  # 只显示前3个的详细信息
                    center_lat, center_lng = h3.cell_to_latlng(child_h3)
                    print(f"  子网格 {i+1} ({child_h3}):")
                    print(f"    中心: ({center_lat:.6f}, {center_lng:.6f})")
                    print(f"    边界点数: {len(boundary)} ✅")
            else:
                abnormal_children += 1
                print(f"  ❌ 子网格 {i+1} ({child_h3}) 边界异常: {len(boundary)} 个点")
                
        except Exception as e:
            abnormal_children += 1
            print(f"  ❌ 子网格 {i+1} ({child_h3}) 边界获取失败: {e}")
    
    print(f"  子网格测试结果: {normal_children} 个正常, {abnormal_children} 个异常")
    
    # 测试边界坐标格式
    print("\n4. 边界坐标格式测试:")
    print("-" * 40)
    
    test_h3 = h3.latlng_to_cell(34.0522, -118.2437, 7)
    boundary = h3.cell_to_boundary(test_h3)
    
    print(f"测试H3索引: {test_h3}")
    print(f"边界格式检查:")
    
    # 检查边界格式是否符合Leaflet要求
    leaflet_boundary = []
    for lat, lng in boundary:
        if isinstance(lat, (int, float)) and isinstance(lng, (int, float)):
            leaflet_boundary.append([lat, lng])
        else:
            print(f"  ❌ 坐标格式异常: ({lat}, {lng})")
    
    if len(leaflet_boundary) == 6:
        print(f"  ✅ 边界格式正确，可用于Leaflet绘制")
        print(f"  边界坐标:")
        for i, (lat, lng) in enumerate(leaflet_boundary):
            print(f"    [{lat:.6f}, {lng:.6f}]")
    else:
        print(f"  ❌ 边界格式异常")
    
    # 检查边界是否闭合
    if len(leaflet_boundary) >= 3:
        first_point = leaflet_boundary[0]
        last_point = leaflet_boundary[-1]
        
        if first_point == last_point:
            print(f"  ✅ 边界已闭合")
        else:
            print(f"  ℹ️  边界未闭合 (Leaflet会自动闭合)")
    
    print(f"\n5. Web界面兼容性检查:")
    print("-" * 40)
    
    # 模拟Web界面中的边界处理
    try:
        # 模拟JavaScript中的处理
        js_compatible_boundary = [[lat, lng] for lat, lng in boundary]
        print(f"  ✅ JavaScript兼容格式: {len(js_compatible_boundary)} 个点")
        
        # 检查坐标范围
        lats = [point[0] for point in js_compatible_boundary]
        lngs = [point[1] for point in js_compatible_boundary]
        
        lat_range = (min(lats), max(lats))
        lng_range = (min(lngs), max(lngs))
        
        print(f"  纬度范围: {lat_range[0]:.6f} ~ {lat_range[1]:.6f}")
        print(f"  经度范围: {lng_range[0]:.6f} ~ {lng_range[1]:.6f}")
        
        # 检查坐标是否合理
        if -90 <= lat_range[0] <= lat_range[1] <= 90:
            print(f"  ✅ 纬度范围正常")
        else:
            print(f"  ❌ 纬度范围异常")
            
        if -180 <= lng_range[0] <= lng_range[1] <= 180:
            print(f"  ✅ 经度范围正常")
        else:
            print(f"  ❌ 经度范围异常")
            
    except Exception as e:
        print(f"  ❌ Web界面兼容性检查失败: {e}")

if __name__ == "__main__":
    test_grid_boundary_drawing()
